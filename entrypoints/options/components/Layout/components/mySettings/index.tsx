/** 我的设置*/
import { editUserAvatar, editUserInfo, editPassword, getCurrentUserInfo } from "@/api/user.ts";
import { nickNameRule, passWordRule } from "@/utils/rule";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { Button, Flex, Form, Input, message, Modal, Select, Upload } from "antd";
import React, { useEffect, useState } from "react";
import "../../index.less";
import "./index.less";
import { uploadFileToOSS } from "@/api/file.ts";
// Tab页签数据配置
const SetupComponent: React.FC = () => {
  const [form] = Form.useForm();
  const [isSendOpen, setIsSendOpen] = useState(false); // 发送验证码弹框
  const [isPassWordOpen, setIsPassWordOpen] = useState(false); // 修改密码弹框
  const [isEdit, setIsEdit] = useState(false); // 点击修改名称按钮
  const [nickName, setNickName] = useState(""); // 修改用户名称字段
  const [userInfo, setUserInfo] = useState<any>({}); // 用户信息
  // 页面首次加载或请求参数变化时，获取用户信息并获取提示词数据

  useEffect(() => {
    getUserInfo();
  }, []);
  // 获取用户信息
  const getUserInfo = () => {
    getCurrentUserInfo({})
      .then((res) => {
        if (res.code == 200) {
          const user = {
            id: res.data.id,
            nickName: res.data.nickName,
            position: res.data.position,
            mobile: res.data.mobile,
            gender: res.data.gender,
            email: res.data.email,
            bizMail: res.data.email,
            avatar: res.data.avatar,
            deptName: res.data.deptName,
            corpName: res.data.corpName,
          };
          setUserInfo(user);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {});
  };
  // 发送验证码弹框确定
  const modalSendOk = () => {
    setIsSendOpen(false);
  };
  // 发送验证码弹框取消
  const modalSendCancel = () => {
    setIsSendOpen(false);
  };
  // 密码弹框确定
  const passWordOk = () => {
    form
      .validateFields()
      .then((values) => {
        editPassword({
          id: userInfo.id,
          oldPassword: form.getFieldValue("oldPassword"),
          password: form.getFieldValue("password"),
          confirmPassword: form.getFieldValue("nextpassword"),
        })
          .then((res) => {
            if (res.code == 200) {
              message.open({
                type: "success",
                content: "修改成功！",
              });
              setIsPassWordOpen(false);
              form.resetFields();
            } else {
              message.open({
                type: "error",
                content: res.msg,
              });
            }
          })
          .catch(() => {});
      })
      .catch((errorInfo) => {});
  };
  // 密码弹框取消
  const passWordCancel = () => {
    setIsPassWordOpen(false);
    form.resetFields();
  };
  // 点击修改名称
  const editName = () => {
    setNickName(userInfo.nickName);
    setIsEdit(true);
  };
  // 点击修改名称btn
  const editNameBtn = () => {
    if (nickName.length < 2) {
      message.open({
        type: "warning",
        content: "用户名至少需要3个字符",
      });
      return;
    }
    if (!nickNameRule.test(nickName)) {
      message.open({
        type: "warning",
        content: "用户名中特殊字符只允许_和-",
      });
      return;
    }
    editUserInfo({
      // account: userInfo.account,
      id: userInfo.id,
      // name: userInfo.name,
      nickName: nickName,
    })
      .then((res) => {
        if (res.code == 200) {
          message.open({
            type: "success",
            content: "修改成功！",
          });
          setIsEdit(false);
          setNickName("");
          getUserInfo();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {});
  };
  // 名称change
  const handleChange = (e) => {
    const value = e.target.value.trim();
    setNickName(value);
  };
  // 点击关闭名称
  const closeName = () => {
    setIsEdit(false);
    setNickName("");
  };
  // 上传文件
  const uploadFile = {
    name: "file",
    multiple: false,
    showUploadList: false,
    accept: ".png,.jpg,.jpeg,.webp,.gif",
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 3; // 限制文件大小为15MB
      if (!isLt2M) {
        message.error("不允许超过3MB!");
        return Promise.reject(); // 返回拒绝的 Promise 阻止上传
      }
      let arr = file.name.split(".");
      let fileName = arr[arr.length - 1] || "";
      let fileFormat = ["png", "jpg", "jpeg", "webp", "gif"];
      if (!fileFormat.includes(fileName)) {
        message.error("图片格式不正确!");
        return Promise.reject(); // 返回拒绝的 Promise 阻止上传
      }
      const reader = new FileReader();
      // 读取文件
      // return new Promise((resolve, reject) => {
      //   reader.onload = function (e) {
      //     const img = new Image();
      //     // 加载图片
      //     img.onload = function () {
      //       const width = img.width;
      //       const height = img.height;
      //       if (width != height || width < 300 || width > 600) {
      //         message.error("图片像素处于150x150到600x600之间");
      //         reject();
      //       } else {
      //         resolve(file); // 验证通过，允许上传
      //       }
      //     };
      //     img.src = e.target.result as string;
      //   };
      //   reader.readAsDataURL(file); // 必须调用以触发 onload
      // });
    },
  };
  const handleCustomRequest = (options) => {
    uploadFileToOSS(options)
      .then((res) => {
        if (res.code == "200") {
          editUserAvatar({ url: res.data?.url })
            .then((ressucc) => {
              if (ressucc.code == "200") {
                setUserInfo({ ...userInfo, ...{ avatar: res.data.absolutePath } });
                message.open({
                  type: "success",
                  content: "修改成功！",
                });
              } else {
                message.open({
                  type: "error",
                  content: ressucc.msg,
                });
              }
            })
            .catch(() => {});
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {});
  };
  return (
    <>
      <Flex className="setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header setup-setting" justify="space-between">
          <h1 className="setup-content-title">我的设置</h1>
        </Flex>
        {/* 信息区 */}
        <Flex className="settings-info" vertical>
          <Flex className="settings-avatar" justify="center" align="center">
            {/* <img src={browser.runtime.getURL("/images/logo.png")} /> */}
            <Upload {...uploadFile} customRequest={handleCustomRequest}>
              {userInfo.avatar ? (
                <img src={userInfo.avatar} alt="avatar" style={{ width: "100%" }} />
              ) : (
                <img src={browser.runtime.getURL("/images/logo.png")} />
              )}
            </Upload>
          </Flex>
          <Flex vertical className="form-item">
            <Flex className="form-item-label">昵称</Flex>
            {!isEdit && (
              <Flex justify="space-between" align="center">
                <Flex className="form-item-val">{userInfo?.nickName ? userInfo?.nickName : "--"}</Flex>
                <Button type="link" onClick={editName}>
                  编辑
                </Button>
              </Flex>
            )}
            {isEdit && (
              <Flex className="edit-name">
                <Input placeholder="请输入昵称" showCount maxLength={30} value={nickName} onChange={handleChange} />
                <Button icon={<CloseOutlined />} className="error" onClick={closeName}></Button>
                <Button icon={<CheckOutlined />} className="btn" onClick={editNameBtn}></Button>
              </Flex>
            )}
          </Flex>
          {userInfo?.corpName && (
            <Flex vertical className="form-item">
              <Flex className="form-item-label">企业名称</Flex>
              <Flex className="form-item-val">{userInfo?.corpName || "--"}</Flex>
            </Flex>
          )}
          {userInfo?.corpName && (
            <Flex vertical className="form-item">
              <Flex className="form-item-label">部门</Flex>
              <Flex className="form-item-val">{userInfo?.deptName || "--"}</Flex>
            </Flex>
          )}
          {userInfo?.corpName && (
            <Flex vertical className="form-item">
              <Flex className="form-item-label">职位</Flex>
              <Flex className="form-item-val">{userInfo?.position || "--"}</Flex>
            </Flex>
          )}
          <Flex vertical className="form-item">
            <Flex className="form-item-label">电子邮箱</Flex>
            <Flex className="form-item-val">{userInfo?.email || "--"}</Flex>
          </Flex>
          <Flex vertical className="form-item">
            <Flex className="form-item-label">手机号码</Flex>
            <Flex className="form-item-val">{userInfo?.mobile || "--"}</Flex>
          </Flex>
          <Flex vertical className="form-item">
            <Flex className="form-item-label">登录密码</Flex>
            <Flex justify="space-between" align="center">
              <Flex className="form-item-val">********</Flex>
              <Button
                type="link"
                onClick={() => {
                  setIsPassWordOpen(true);
                }}
              >
                修改密码
              </Button>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
      <Modal
        title="为保证账号安全，请先验证身份"
        width={360}
        centered={true}
        open={isSendOpen}
        onOk={modalSendOk}
        onCancel={modalSendCancel}
      >
        <Form>
          <Form.Item label="">
            <Select options={[{ value: "176***4335", label: "176***4335" }]} defaultValue="176***4335"></Select>
          </Form.Item>
          <Flex justify="space-between">
            <Form.Item label="">
              {/* <Form.Item label="" validateStatus="error" help="验证码不正确"> */}
              <Input placeholder="请输入验证码" style={{ width: "195px" }} />
            </Form.Item>
            <Button>发送验证码</Button>
          </Flex>
        </Form>
      </Modal>
      <Modal
        title="请设置登录密码"
        centered={true}
        width={380}
        open={isPassWordOpen}
        onOk={passWordOk}
        onCancel={passWordCancel}
      >
        <Form form={form}>
          <Form.Item
            label=""
            name="oldPassword"
            rules={[{ required: true, message: "请输入旧密码" }]}
            getValueFromEvent={(e) => e.target.value.replace(/\s+/g, "")}
          >
            <Input.Password placeholder="请输入旧密码" />
          </Form.Item>
          <Form.Item
            label=""
            name="password"
            rules={[
              { required: true, message: "密码格式不正确" },
              { pattern: passWordRule, message: "密码格式不正确" },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("oldPassword") !== value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("新密码不能与旧密码相同"));
                },
              }),
            ]}
            getValueFromEvent={(e) => e.target.value.replace(/\s+/g, "")}
          >
            <Input.Password placeholder="请输入8-16位字母、数字或符号2种及以上组合" />
          </Form.Item>
          <Form.Item
            label=""
            name="nextpassword"
            rules={[
              { required: true, message: "密码格式不正确" },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("password") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error("两次密码输入不一致"));
                },
              }),
            ]}
            getValueFromEvent={(e) => e.target.value.replace(/\s+/g, "")}
          >
            <Input.Password placeholder="请再输一次密码" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default SetupComponent;
