import React, { useEffect, useRef, useState } from "react";
import SidePanelLayout from "@/entrypoints/sidepanel/components/layout/web-index.tsx";
import classNames from "classnames";
import { NOTE_DETAIL_STORAGE_KEY, NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage.ts";
import { getLocationNotes, getQueryString, getUrlNoQuery, initNotelist, setNote } from "@/utils/notes";
import { getUserInfo } from "@/utils/auth";
import { Button, Divider, Flex, message, theme, Tooltip } from "antd";
import { createStrokeService } from "../annotator";
import { DoubleLeftOutlined, DoubleRightOutlined } from "@ant-design/icons";
import { PinIcon } from "@/config/menu";
import { SHADOW_SIDE_PANEL, SIDE_PANEL_WRAPPER_ID } from "@/entrypoints/content/sidepanel";
import styles from "./index.module.less";
import { PermissionProvider } from "@/entrypoints/sidepanel/components/PermissionProvider";

const { useToken } = theme;

const OpenImage = () => (
  <img
    draggable={false}
    className={classNames(styles["btn-wrapper-img"])}
    src={browser.runtime.getURL("/images/logo.png")}
    alt=""
  />
);

const CloseImage = () => (
  <svg transform="rotate(45)" width="20" height="20" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.353 4.853H6.14719V0.647003C6.14719 0.258763 5.88824 0 5.5 0C5.11176 0 4.853 0.258763 4.853 0.647003V4.853H0.647003C0.258763 4.853 0 5.11176 0 5.5C0 5.88824 0.258763 6.147 0.647003 6.147H4.853V10.353C4.853 10.7412 5.11176 11 5.5 11C5.88824 11 6.147 10.7412 6.147 10.353V6.14719H10.353C10.7412 6.14719 11 5.88843 11 5.50019C11.0002 5.11176 10.7412 4.853 10.353 4.853Z"
      fill="#bbb"
    />
  </svg>
);
const SidePanelWrapper: React.FC<{ container: HTMLDivElement }> = ({ container }) => {
  const { token } = useToken();
  let offsetX: number, offsetY: number;
  const toggleWrapperRef = useRef<HTMLDivElement>(null);
  const [dragging, setDragging] = useState<boolean>(false);
  const [expand, setExpand] = useState<boolean>(false);
  const [list, setList] = useState<any>([]);
  const currentUrl = useRef(getUrlNoQuery());
  const [open, setOpen] = useState<boolean>(false);
  const guestRef = useRef(null);

  useEffect(() => {
    getUserInfo().then((res) => {
      if (res) {
        getNotes("init");
      }
    });

    let sinoKey = sessionStorage.getItem("sino-tap-key");
    const handleNoteListChanged = async (changes) => {
      let noteInfo = changes[NOTE_MODIFY_STORAGE_KEY + sinoKey];
      if (noteInfo) {
        getNotes(noteInfo.newValue.updateType, noteInfo.newValue.id);
      }
      // 展开
      if (changes[NOTE_DETAIL_STORAGE_KEY + sinoKey] || changes["characterDataTime"] || changes["characterData"]) {
        container.classList.add(styles["expand"]);
        const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
        const div = shadowDom.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
        let width = (await browser.storage.local.get("siderWidth"))?.siderWidth || 400;
        div.style.transform = "translateX(0px)";
        div.style.width = width + "px";
        setExpand(true);
      }
      if (changes["userInfo"]) {
        // 登录
        if (changes["userInfo"].newValue) {
          getNotes("init");
        }
        // 登出
        if (changes["userInfo"].oldValue) {
          setList([]);
          getLocationNotes().then((noteList) => {
            guestRef.current._delAllNoteNotice(noteList.map((x) => x.id));
          });
        }
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  // 监听主页面 URL 变化
  useEffect(() => {
    let flag = false;
    // 创建一个 MutationObserver 实例来监视指定节点的子节点变化
    const observer = new MutationObserver(async (mutationsList) => {
      if (flag) {
        // 如果标志位为true，则停止进一步的处理
        return;
      }
      flag = true;

      for (let mutation of mutationsList) {
        if (mutation.type === "childList" && currentUrl.current !== getUrlNoQuery()) {
          // 在这里可以执行一些操作，处理 URL 变化事件
          currentUrl.current = getUrlNoQuery();
          const noteList: any = await getCurrentLocationList(currentUrl.current, "init");
          setList(noteList);
          guestRef.current._delAllNoteNotice(noteList.map((x) => x.id));
          initNotelist(guestRef.current, noteList);
          break;
        }
      }

      // 完成处理后，重置标志位
      flag = false;
    });

    // 选择要监视变化的节点，可以根据具体情况选择合适的节点
    const targetNode = document.body;

    // 配置 MutationObserver 实例以监视子节点的变化
    const observerConfig = { childList: true, subtree: true };

    observer.observe(targetNode, observerConfig);

    return () => {
      observer.disconnect();
    };
  }, []);
  const fetchRequest = useFetchRequest();

  // 获取当前页便签，存储到 local
  const getCurrentLocationList = async (url, type?: string) => {
    const noteId = getQueryString("noteId") || "";
    return new Promise((resolve) => {
      fetchRequest({
        api: "listNote",
        params: {
          query: "",
          url,
          noteId: noteId,
        },
        callback: (res) => {
          if (res.code === 200) {
            const list = (res.data || []).filter((item, index, self) => {
              return self.findIndex((t) => t.id === item.id) === index;
            });
            if (type === "init") {
              list.forEach((item) => {
                item.displayFlag = 1;
              });
            }
            setNote(list);
            cacheSet("noteList", list);
            resolve(list);
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
            resolve([]);
          }
        },
      });
    });
  };

  const setNoteShowClose = async (type: boolean) => {
    if (list.length === 0) return;
    // const promises = list.map((note) => {
    //   note.displayFlag = type ? "1" : "2";
    //   return fetchRequest({
    //     api: "editNote",
    //     params: note,
    //     callback: () => {},
    //   });
    // });

    // try {
    //   await Promise.all(promises);
    //   getNotes("edit");
    //   guestRef.current && guestRef.current._setNoteShowClose(type ? "1" : "2", type);
    // } catch (error) {
    //   console.error("请求出错", error);
    // }
    guestRef.current && guestRef.current._setNoteShowClose(type ? "1" : "2", type);
  };

  const getNotes = async (type?: string, id?: string) => {
    const noteList: any = await getCurrentLocationList(currentUrl.current, type);
    setList(noteList);
    if (!guestRef.current) {
      guestRef.current = createStrokeService(noteList);
    } else {
      if (type === "init") {
        initNotelist(guestRef.current, noteList);
      }
    }
  };

  const handleToggleBtnClick = async () => {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDom.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
    let width = (await browser.storage.local.get("siderWidth"))?.siderWidth || 400;
    if (expand) {
      div.style.transform = `translateX(${width}px)`;
      container.classList.remove(styles["expand"]);
      setExpand(false);
    } else {
      container.classList.add(styles["expand"]);
      div.style.transform = "translateX(0px)";
      div.style.width = width + "px";
      setExpand(true);
    }
  };

  const handleDrag = (e) => {
    toggleWrapperRef.current.style.left = e.clientX - offsetX + "px";
    toggleWrapperRef.current.style.top = e.clientY - offsetY + "px";
  };

  const handleToggleBtnDragStart = (e) => {
    setDragging(true);
    offsetX = e.clientX - toggleWrapperRef.current.getBoundingClientRect().left;
    offsetY = e.clientY - toggleWrapperRef.current.getBoundingClientRect().top;
    document.addEventListener("dragover", handleDrag);
  };

  const handleToggleBtnDragEnd = () => {
    setDragging(false);
    document.removeEventListener("dragover", handleDrag);
    toggleWrapperRef.current.style.left = "0";
  };

  return (
    <PermissionProvider>
      <div
        id="annotator-stroke"
        style={{ position: "absolute", zIndex: 9, pointerEvents: "none", opacity: expand ? 1 : 0 }}
      />
      <Flex
        className={classNames(styles["toggle-btn-wrapper"])}
        ref={toggleWrapperRef}
        style={{ bottom: "125px" }}
        vertical
      >
        {list.length > 0 && (
          <Flex>
            {open && (
              <Flex
                onMouseEnter={() => {
                  setOpen(true);
                }}
                onMouseLeave={() => {
                  setOpen(false);
                }}
                style={{
                  position: "absolute",
                  left: "-48px",
                  top: "-84px",
                  zIndex: "10000 !important",
                  paddingBottom: "8px",
                }}
              >
                <Flex
                  vertical
                  style={{
                    width: "40px",
                    borderRadius: "8px",
                    boxShadow:
                      "rgba(0, 0, 0, 0.08) 0px 6px 16px 0px, rgba(0, 0, 0, 0.12) 0px 3px 6px -4px, rgba(0, 0, 0, 0.05) 0px 9px 28px 8px",
                    background: token.colorBgBase,
                    fontSize: token.fontSizeSM,
                  }}
                  align="center"
                >
                  <Tooltip
                    placement="left"
                    title="一键展开"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DoubleLeftOutlined />}
                      style={{ padding: token.paddingXS, margin: "4px" }}
                      type="text"
                      onClick={() => setNoteShowClose(true)}
                    ></Button>
                  </Tooltip>
                  <Divider orientationMargin="0" style={{ margin: 0 }} />
                  <Tooltip
                    placement="left"
                    title="一键收起"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DoubleRightOutlined />}
                      style={{ padding: token.paddingXS, margin: "4px" }}
                      type="text"
                      onClick={() => setNoteShowClose(false)}
                    ></Button>
                  </Tooltip>
                </Flex>
              </Flex>
            )}
            <Tooltip
              placement="left"
              title={`本页便签： ${list.length}`}
              getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
            >
              <Flex
                align="center"
                justify="center"
                gap="3"
                style={{
                  position: "absolute",
                  left: "-48px",
                  zIndex: "10000 !important",
                  width: "40px",
                  height: "40px",
                  borderRadius: "8px",
                  cursor: "pointer",
                  boxShadow:
                    "0px 6px 16px 0px rgba(0, 0, 0, 0.08),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 9px 28px 8px rgba(0, 0, 0, 0.05)",
                  background: token.colorBgBase,
                  fontSize: token.fontSizeSM,
                }}
                onMouseEnter={() => {
                  setOpen(true);
                }}
                onMouseLeave={() => {
                  setOpen(false);
                }}
              >
                <Flex
                  style={{
                    transform: "scale(0.608)",
                  }}
                >
                  {PinIcon}
                </Flex>
                <Flex>{list.length}</Flex>
              </Flex>
            </Tooltip>
          </Flex>
        )}
        <Flex
          className={classNames([
            styles[expand ? "toggle-btn-expand" : "toggle-btn"],
            styles[dragging && "dragging"],
            expand && styles["expand"],
          ])}
          draggable={!expand}
          onClick={handleToggleBtnClick}
          onDragStart={handleToggleBtnDragStart}
          onDragEnd={handleToggleBtnDragEnd}
        >
          {expand ? <CloseImage /> : <OpenImage />}
        </Flex>
      </Flex>
      <SidePanelLayout />
    </PermissionProvider>
  );
};

export default SidePanelWrapper;
