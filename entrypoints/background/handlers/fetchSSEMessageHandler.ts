import useEventSource from "@/hooks/useEventSource.ts";
import { IFetchSSERequestMessage } from "@/types/message";
import { FETCH_REQUEST_TYPE, FETCH_RESPONSE_TYPE } from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import { getTenantId } from "@/utils/auth.ts";

/** 普通网络请求端口的名称 */
export const FETCH_SSE_PORT_NAME = "fetchSSE";

const registerFetchSSEMessageHandler = () => {
  const handleFetchSSEConnected = () => {
    browser.runtime.onConnect.addListener((port) => {
      if (port.name !== FETCH_SSE_PORT_NAME) return;
      console.debug(`${FETCH_SSE_PORT_NAME} 端口已建立连接`);
      let eventSourceInstance: ReturnType<typeof useEventSource> | null = null; // To store the ev
      port.onMessage.addListener(async (msg: IFetchSSERequestMessage) => {
        console.debug(`background ${FETCH_SSE_PORT_NAME} 收到了消息`, msg);
        if (msg.instruct == "stop") {
          eventSourceInstance?.stop();
          port.postMessage({
            process: "cancel",
            type: FETCH_RESPONSE_TYPE,
            msg: "已经取消加载了哦！",
          });
          return
        }
        if (msg.type === FETCH_REQUEST_TYPE) {
          if (!navigator.onLine) {
            port.postMessage({
              process: "error",
              type: FETCH_RESPONSE_TYPE,
              msg: "网络连接已断开，请检查网络连接",
            });
          }
          const tenantId = await getTenantId()
          const eventSource = useEventSource({
            url: msg.body.insId
              ? `${import.meta.env["VITE_API_BASE"]}${import.meta.env["VITE_API_BASE_INS"]}${msg.url}`
              : `${import.meta.env["VITE_AI_API_BASE"]}${msg.url}`,
            method: "POST",
            headers: { ...msg.headers, ...{ tenantId: tenantId } },
            body: msg.body,
            onMessage: (message) => {
              port.postMessage({
                url: msg.url,
                process: "onmessage",
                type: FETCH_RESPONSE_TYPE,
                ...message,
              });
            },
            onMessageFile: (message) => {
              port.postMessage({
                url: msg.url,
                process: "onmessagefile",
                type: FETCH_RESPONSE_TYPE,
                ...message,
              });
            },
            onMessageEnd: (data) => {
              port.postMessage({
                url: msg.url,
                process: "onmessage",
                type: FETCH_RESPONSE_TYPE,
                ...data,
              });
            },
            onClose: () => {
              port.postMessage({
                url: msg.url,
                process: "finished",
                type: FETCH_RESPONSE_TYPE,
              });
            },
            onError: () => {
              port.postMessage({
                url: msg.url,
                process: "error",
                type: FETCH_RESPONSE_TYPE,
              });
            },
          });
          if (msg.instruct === "start") {
            eventSource.start(msg.query);
          } else if (msg.instruct === "stop") {
            port.postMessage({
              url: msg.url,
              instruct: "stop",
              process: "finished",
              type: FETCH_RESPONSE_TYPE,
            });
            eventSource.stop();
            console.log("Event stream stopped"); // 更清晰的日志
          }
        }
      });
      port.onDisconnect.addListener((port) => {
        console.debug(`content主动断开了 ${FETCH_SSE_PORT_NAME} 端口的连接`);
      });
    });
  };

  // 在安装时与启动时注册消息响应事件，确保浏览器进程全部关闭再打开后连接依然可以打开
  handleFetchSSEConnected();
  browser.runtime.onStartup.addListener(handleFetchSSEConnected);
};

export default registerFetchSSEMessageHandler;
