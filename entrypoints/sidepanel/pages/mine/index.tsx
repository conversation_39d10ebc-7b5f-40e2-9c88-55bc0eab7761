import { UserInfo } from "@/utils/auth.ts";
import { <PERSON><PERSON>, Flex, message } from "antd";
import React, { useEffect } from "react";
import { usePermissions } from "../../components/PermissionProvider";
import TopTitle from "../../components/TopTitle";
import "./index.less";

const MineComponent: React.FC = () => {
  const { userInfo, setUserInfo, point } = usePermissions();
  const fetchRequest = useFetchRequest();

  const handleQuit = () => {
    // fetchRequest({
    //   api: "logout",
    //   params: {},
    //   callback(res) {
    //     if (res.success || res.code === 200) {
    //       localStorage.setItem("user", "{}");
    //       setUserInfo({} as UserInfo);
    //       browser.runtime.sendMessage({ type: "updateOptions" });
    //       browser.storage.local.remove(["userInfo"]);

    //       // 插件端清除
    //       browser.runtime.sendMessage({ ChromeClearCookie: true });
    //       // 网页端清除
    //       document.cookie = `url=https://scrm.sino-bridge.com:8098; userInfo={}`;
    //     } else {
    //       message.error(res.msg);
    //     }
    //   },
    // });
    cacheSet("userInfo", "");
    browser.runtime.sendMessage({ type: "updateOptions" });
    // 插件端清除
    browser.runtime.sendMessage({ ChromeClearCookie: true });
    // 网页端清除
    document.cookie = `url=https://scrm.sino-bridge.com:8098; userInfo={}`;
    browser.storage.local.remove("tenantId");
    browser.storage.local.remove("token");
    browser.storage.local.remove("permissions");
    browser.storage.local.remove("refreshTokenKey");
  };

  return (
    <>
      <TopTitle title="个人中心"></TopTitle>
      {userInfo?.id && (
        <div className="side-panel-content-mine mine">
          <div className="mine-info">
            <Flex className="mine-info-name" align="center">
              <div className="mine-info-name-left">
                <div className="mine-info-name-avatar">
                  {userInfo.avatar ? <img src={userInfo.avatar} alt="" /> : userInfo?.nickName[0]}
                </div>
              </div>
              <Flex className="mine-info-name-right" justify="space-between" align="center">
                <div className="mine-info-name-title">{userInfo?.nickName}</div>
                {/* <div className="mine-info-name-position">{userInfo?.position || "未设置"}</div> */}
                <div className="mine-info-name-position">
                  {point?.effectivePoint} / {point?.totalPoint}
                </div>
              </Flex>
            </Flex>
            {userInfo?.corpName && (
              <Flex className="mine-info-crop" vertical>
                <Flex className="mine-info-top" align="center">
                  <Flex className="mine-info-label">企业</Flex>
                  <Flex className="mine-info-content">{userInfo?.corpName}</Flex>
                </Flex>
                <Flex className="mine-info-bottom" align="center">
                  <Flex className="mine-info-label">部门</Flex>
                  <Flex className="mine-info-content">{userInfo?.deptName}</Flex>
                </Flex>
              </Flex>
            )}
            <Flex className="mine-info-phone" vertical>
              <Flex className="mine-info-top" align="center">
                <Flex className="mine-info-label">手机</Flex>
                <Flex className="mine-info-content">{userInfo?.mobile || "未设置"}</Flex>
              </Flex>
              <Flex className="mine-info-bottom" align="center">
                <Flex className="mine-info-label">邮箱</Flex>
                <Flex className="mine-info-content">{userInfo?.email || "未设置"}</Flex>
              </Flex>
            </Flex>
            <Flex className="mine-info-submit">
              <Button className="mine-info-submit-btn" size="large" type="primary" onClick={() => handleQuit()}>
                退出登录
              </Button>
            </Flex>
          </div>
        </div>
      )}
    </>
  );
};

export default MineComponent;
