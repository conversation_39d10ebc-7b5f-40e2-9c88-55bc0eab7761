import { Col, Flex, Row, Typography, Spin, theme } from "antd"; // 引入 Spin 组件
import React, { useEffect, useState } from "react";
import IconFont from "@/components/IconFont";
import AnswerTips from "./answerTips";
import TopTitle from "../../components/TopTitle";
import { getToken, getTenantId } from "@/utils/auth.ts";
import "./index.less";
import EmptyData from "@/components/EmptyData";
const LAB_FEATURES_ENABLED: string = import.meta.env["VITE_ENABLE_LAB_FEATURES"];

const { useToken } = theme;
const Tool: React.FC = () => {
  const fetchRequest = useFetchRequest();
  const [selectedId, setSelectedId] = useState("");
  const [sceneList, setSceneList] = useState([]);
  const [loading, setLoading] = useState(false); // 新增状态变量
  const { token: csstoken } = useToken();

  const handleBack = () => {
    setSelectedId("");
  };

  const getData = () => {
    setLoading(true); // 开始加载数据时设置 loading 为 true
    fetchRequest({
      api: "getSceneList",
      params: {},
      callback(res) {
        setLoading(false); // 数据加载完成后设置 loading 为 false
        if (res.code === 200) {
          setSceneList(res.data);
        }
      },
    });
  };

  const updateUrlParams = (url, params) => {
    // Split into base URL and hash (if any)
    const [baseUrl, hashWithParams] = url.split("#");

    // Process the base URL (remove all query params)
    const urlObj = new URL(baseUrl);
    urlObj.search = ""; // Clear all query params from the base URL

    let result = urlObj.toString();

    // If there's a hash, handle its query params
    if (hashWithParams) {
      const [hashPath, hashQuery] = hashWithParams.split("?");
      const hashParams = new URLSearchParams(hashQuery || "");

      // Update hash params (add/replace all given params)
      Object.keys(params).forEach((key) => {
        hashParams.delete(key);
        hashParams.set(key, params[key]);
      });

      // Reconstruct the hash with updated params
      let newHash = hashPath;
      if (hashParams.toString()) {
        newHash += `?${hashParams.toString()}`;
      }

      // Append the hash to the result
      result = `${result.split("#")[0]}#${newHash}`;
    }

    return result;
  };

  const openUrl = async (url: string) => {
    let updatedUrl = url;
    const token = await getToken();
    const tenantId = await getTenantId();
    if (url.includes("token")) {
      updatedUrl = updateUrlParams(url, {
        tenantid: tenantId,
        token: token,
      });
    }
    window.open(updatedUrl, "_blank");
  };

  useEffect(() => {
    getData();
  }, []); // 空依赖数组，确保只在组件挂载时调用一次

  return (
    <>
      {!selectedId ? (
        <Flex vertical className="tool-page-container">
          <TopTitle title="场景"></TopTitle>
          <>
            <Flex className="tool-group-con">
              {loading ? (
                <Flex
                  style={{
                    flex: "1 1 100%",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Spin /> {/* 如果 loading 为 true，显示加载指示器 */}
                </Flex>
              ) : sceneList && sceneList.length > 0 ? (
                <Flex
                  vertical
                  style={{
                    flex: "1 1 100%",
                  }}
                >
                  {sceneList.map((item: any) => {
                    return (
                      <Flex className="tool-group-sign" vertical key={item.catId}>
                        <Flex className="tool-group-title">{item.catName}</Flex>
                        <Row gutter={16}>
                          {item.scenes.map((val: any) => {
                            return (
                              <Col className="sino-gutter-row" span={12} key={val.sceneId}>
                                <Flex className="tool-item" onClick={() => openUrl(val.sceneUrl)}>
                                  {val.sceneIconUrl ? (
                                    <img src={val.sceneIconUrl} alt="" className="icon-img" />
                                  ) : (
                                    <IconFont type="FileSearchOutline" className="icon" />
                                  )}
                                  <Flex className="sino-gutter-text" vertical>
                                    <Typography.Text>{val.sceneName}</Typography.Text>
                                    <Typography.Text type="secondary">{val.sceneDesc}</Typography.Text>
                                  </Flex>
                                  {!!val.pointsCost && val.pointsCost > 0 && (
                                    <Flex className="sino-gutter-point">
                                      <span style={{ color: csstoken.blue }}>{val.pointsCost}&nbsp;</span>
                                      <IconFont type="PointsCost" className="icon" />
                                    </Flex>
                                  )}
                                </Flex>
                              </Col>
                            );
                          })}
                        </Row>
                      </Flex>
                    );
                  })}
                </Flex>
              ) : (
                <EmptyData description="暂无数据"></EmptyData>
              )}
            </Flex>
          </>
        </Flex>
      ) : (
        <>{selectedId === "1" && <AnswerTips handleBack={handleBack} />}</>
      )}
    </>
  );
};

export default Tool;
