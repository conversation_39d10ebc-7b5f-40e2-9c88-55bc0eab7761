import React, { useCallback, useEffect, useRef, useState } from "react";
import Empty from "./components/Empty/index";
import NoteCard from "./components/NoteCard/index";
import NoteDetail from "./components/NoteDetail/index";
import {
  Button,
  Col,
  Dropdown,
  Flex,
  Form,
  Input,
  Menu,
  message,
  Modal,
  Row,
  Segmented,
  Select,
  Spin,
  Tag,
  theme,
  Typography,
} from "antd";
import { debounce } from "@/utils/debounce.ts";
import classNames from "classnames";
import setModifyItem, { MENU_NAME_STORAGE_KEY, NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage.ts";
import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  UngroupOutlined,
} from "@ant-design/icons";
import NoteGroupModal from "@/components/NoteGroupModal";
import TopTitle from "../../components/TopTitle";
import NodeGroupDetail from "./components/NodeGroupDetail";
import "./index.less";
const { useToken } = theme;
// 列表查询的初始化参数
export const searchParamsInit: PageAPIRequest<PageNotesRequest> = {
  pageNum: 1,
  pageSize: 10000,
  orders: [{ column: "create_time", asc: false }],
  entity: {
    type: "note_type_all",
    query: "",
    url: "",
  },
};

const NotePage: React.FC<{ handleNavigate; defaultNote?: any | null }> = ({ handleNavigate, defaultNote }) => {
  const segmentedHeight = useRef(null);
  const { token } = useToken();
  const localTabData = localStorage.getItem("searchTab");
  // 搜索参数
  const [searchText, setSearchText] = useState<string>("");

  const [currentUrl, setCurrentUrl] = useState<string>("");
  const [selectedItem, setSelectedItem] = useState<string>("note_type_all");
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PageNotesRequest>>(searchParamsInit);
  // 当前显示的提示词分页数据
  const [notePageData, setNotePageData] = useState([]);
  const fetchRequest = useFetchRequest();
  const [searchIcon, setSearchIcon] = useState(true);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const [searchTab, setSearchTab] = useState(localTabData ?? "all");
  const { TextArea } = Input;
  const [detail, setDetail] = useState(null);
  const [notePageDataCount, setNotePageDataCount] = useState(0);
  const [notePageDataTotal, setNotePageDataTotal] = useState(null);
  const [currentTab, setCurrentTab] = useState<string>("2");
  const [groupId, setGroupId] = useState<string>(""); // 当前组id
  const [groupInfo, setGroupInfo] = useState({}); // 当前组信息
  const [currentPage, setCurrentPage] = useState<string>("");
  const [isGroupOpen, setIsGroupOpen] = useState(false);
  const [groupDetail, setGroupDetail] = useState(false);
  const tagList = [
    { key: "note_type_all", label: "全部" },
    { key: "channel_type_created", label: "我创建的" },
    { key: "channel_type_involved", label: "协同" },
    { key: "note_type_current", label: "当前页" },
    { key: "note_type_unread", label: "未读" },
  ];
  const [modalVisible, setModalVisible] = useState(false);
  useEffect(() => {
    if (currentTab) {
      setCurrentPage(window.location.href);
    }
  }, []);

  useEffect(() => {
    if (defaultNote) {
      const { note } = defaultNote;
      handleDetail(note);
      setIsOpen(true);
    }
  }, [defaultNote]);
  // 组件初次渲染获取网页地址
  useEffect(() => {
    getCurrentUrl();

    window?.document?.addEventListener("visibilitychange", handleLocationChange);
    activateTabListener();
    return () => {
      window?.document?.removeEventListener("visibilitychange", handleLocationChange);
      deactivateTabListener();
    };
  }, []);

  // 组件注册时，便开始监听用户自身便签数据的变化，一旦变化，重新渲染列表
  useEffect(() => {
    let sinoKey = sessionStorage.getItem("sino-tap-key");
    const handleNoteListChanged = (changes) => {
      // type 跟channelType 这两个字端有点复合
      if (changes[NOTE_MODIFY_STORAGE_KEY + sinoKey]) {
        if (currentTab == "2") {
          if (selectedItem == "channel_type_created" || selectedItem == "channel_type_involved") {
            handleNoteList({
              entity: { query: searchText, url: "", channelType: selectedItem, type: "note_type_all" },
            });
          } else if (selectedItem == "note_type_current") {
            handleNoteList({
              entity: { query: searchText, url: window.location.href, type: "note_type_all" },
            });
          } else {
            handleNoteList({
              entity: { query: searchText, url: "", type: selectedItem },
            });
          }
        }
      }
      if (changes["uploadGroupData"] && currentTab == "1") {
        browser.storage.local.get("uploadGroupData").then((result) => {
          const info = result.uploadGroupData;
          if (info.type == "add" || info.type == "edit") {
            getFlatList();
          }
        });
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, [currentTab, selectedItem, searchText]);

  const getListByUrl = (url) => {
    setCurrentUrl(url);
    searchParams.entity.url = localTabData === "current" ? url : "";
    setSearchParams(searchParams);
  };

  const getCurrentUrl = async () => {
    if (browser.tabs) {
      browser.tabs.query({ active: true, lastFocusedWindow: true }).then((tabs) => {
        if (tabs && tabs.length > 0 && tabs[0].url) {
          const url = tabs[0].url;
          getListByUrl(url);
        }
      });
    } else {
      const url = window.location.href;
      getListByUrl(url);
    }
  };

  const browserTabAction = (url) => {
    setCurrentUrl(url);
    setSearchTab(localTabData);
    searchParams.entity.url = localTabData === "current" ? url : "";
  };

  const handleLocationChange = () => {
    if (document.visibilityState === "visible") {
      const url = window.location.href;
      if (currentUrl !== url) {
        browserTabAction(url);
      }
    }
  };

  /** 浏览器页签激活状态变更处理器 */
  const tabActivatedHandler = (activeInfo: any) => {
    // 获取新激活的标签页的详细信息
    browser.tabs.get(activeInfo.tabId).then((res) => {
      browserTabAction(res.url);
    });
    browser.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (tabId === activeInfo.tabId) {
        browserTabAction(tab.url);
      }
    });
  };

  /** 注册标签页变更事件监听器 */
  const activateTabListener = () => {
    browser.tabs && browser.tabs.onActivated.addListener(tabActivatedHandler);
  };

  /** 销毁标签页变更事件监听器 */
  const deactivateTabListener = () => {
    browser.tabs && browser.tabs.onActivated.removeListener(tabActivatedHandler);
  };

  /** 分页查询便签 */
  const handleNoteList = (params = {}) => {
    setLoading(true);
    fetchRequest({
      api: "pageNote",
      params: {
        pageSize: 10000,
        pageNum: 1,
        orders: [{ column: "create_time", asc: false }],
        ...params,
      },
      callback: (res) => {
        if (res.code === 200) {
          setNotePageData(res.data.page.records);
          setNotePageDataCount(res.data.count);
          setNotePageDataTotal(res.data.page.total);
        } else {
          if (res.code !== 401) {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        }
        setLoading(false);
      },
    });
  };
  const inputFocus = () => {
    if (!searchIcon) return;

    setSearchIcon(false);
  };

  const inputBlur = () => {
    setSearchIcon(true);
  };

  const handleNoteSearch = (params: { value: string; url: string; currentTab: string; type?: string }) => {
    let currentSelect = params.currentTab || currentTab;
    if (currentSelect == "2") {
      handleNoteList({
        pageNum: 1,
        orders: [{ column: "create_time", asc: false }],
        entity: { query: params.value, url: params.url, type: params.type },
      });
    }
    if (currentSelect == "1") {
      getFlatList(params.value);
    }
  };

  const debounceNoteSearch = useCallback(
    debounce((params) => {
      handleNoteSearch(params); // 调用搜索处理函数
    }, 500),
    [], // 这里确保 debounce 只创建一次
  );
  const inputChange = (e) => {
    const value = e.target.value.trim();
    if (!value) {
      setSearchIcon(true);
    }
    setSearchText(value);
    setSearchParams({
      ...searchParams,
      ...{
        pageNum: 1,
        pageSize: 10000,
      },
      entity: {
        ...searchParams.entity,
        ...{ query: value },
      },
    });
  };
  useEffect(() => {
    if (selectedItem == "channel_type_created" || selectedItem == "channel_type_involved") {
      debounceNoteSearch({ value: searchText, url: "", currentTab: currentTab, type: "note_type_all" });
    } else if (selectedItem == "note_type_current") {
      debounceNoteSearch({ value: searchText, url: currentPage, currentTab: currentTab, type: "note_type_all" });
    } else {
      debounceNoteSearch({ value: searchText, url: "", currentTab: currentTab, type: selectedItem });
    }
  }, [searchText, currentPage]);

  // 便签详情
  const handleDetail = (noteElement) => {
    setDetail(noteElement);
    setModifyItem(MENU_NAME_STORAGE_KEY, {
      key: new Date().getTime(),
      value: "返回",
    });
  };

  // 回到列表页 或者回到组详情页面
  const handleBack = () => {
    if (currentTab == "1") {
      // 跳转到组详情页面
      setGroupDetail(false);
      setIsOpen(false);
      handleDetail(1);
    } else {
      setDetail(null);
      setModifyItem(MENU_NAME_STORAGE_KEY, {
        key: new Date().getTime(),
        value: "",
      });
      handleNavigate();
    }
  };

  // 回到便签组
  const handleGroupBack = () => {
    setDetail(null);
    setCurrentTab("1");
    setGroupDetail(false);
    setModifyItem(MENU_NAME_STORAGE_KEY, {
      key: new Date().getTime(),
      value: "",
    });
    getFlatList();
    handleNavigate();
  };

  // 获取组数据
  const getFlatList = (val?: string) => {
    setLoading(true);
    fetchRequest({
      api: "flatList",
      params: {
        keyword: val ? val : searchText,
      },
      callback: (res) => {
        if (res.code === 200) {
          setNotePageData(res.data);
          setNotePageDataCount(0);
          setNotePageDataTotal(res.data.length);
        } else {
          if (res.code !== 401) {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        }
        setLoading(false);
      },
    });
  };
  const handleTabChange = (targetKey: string) => {
    setLoading(true);
    if (targetKey === "便签组") {
      setCurrentTab("1");
    } else {
      setCurrentTab("2");
    }
    setNotePageData([]);
    setNotePageDataCount(0);
    setNotePageDataTotal(0);
    if (targetKey === "便签组") {
      getFlatList();
    } else {
      handleNoteList(searchParams);
    }
  };
  const deleItemHandler = () => {
    handleNoteList(searchParams);
  };
  // tab 选中
  const tabSelect = (item) => {
    setSelectedItem(item.key);
    let tabType = "";
    let channelType = "";
    let url = "";
    if (item.key == "channel_type_created" || item.key == "channel_type_involved") {
      tabType = "note_type_all";
      channelType = item.key;
    } else if (item.key == "note_type_current") {
      url = window.location.href;
      tabType = "note_type_all";
    } else {
      tabType = item.key;
      channelType = "";
    }
    setSearchParams({
      ...searchParams,
      ...{
        pageNum: 1,
        pageSize: 10000,
      },
      entity: {
        ...searchParams.entity,
        ...{ type: tabType, channelType: channelType, url: url },
      },
    });
    handleNoteList({
      ...searchParams,
      ...{
        pageNum: 1,
        pageSize: 100000,
      },
      entity: {
        ...searchParams.entity,
        ...{ type: tabType, channelType: channelType, url: url },
      },
    });
  };
  // 删除
  const delGroup = (id: string) => {
    setLoading(true);
    fetchRequest({
      // 先或者这个组里面的便签
      api: "notesGroupPage",
      params: {
        pageNum: 1,
        pageSize: 10000,
        orders: [{ column: "create_time", asc: false }],
        entity: {
          query: "",
          groupId: id,
        },
      },
      callback: (group) => {
        if (group.code === 200) {
          fetchRequest({
            api: "delNotesGroup",
            params: [id],
            callback: (res) => {
              setLoading(false);
              if (res.code === 200) {
                message.open({
                  type: "success",
                  content: "删除成功",
                });
                getFlatList();
                group.data.page.records.forEach((item) => {
                  let note = item;
                  window.postMessage({ type: "delNoteNotice", note }, "*");
                });
                browser.storage.local.set({
                  uploadGroupData: {
                    date: Date.now(),
                    type: "del",
                  },
                });
              } else {
                if (res.code !== 401) {
                  message.open({
                    type: "error",
                    content: res.msg,
                  });
                }
              }
            },
          });
        } else {
          message.open({
            type: "error",
            content: group.msg,
          });
        }
      },
    });
  };
  const disbandGroup = (id: string) => {
    setLoading(true);
    fetchRequest({
      api: "notesGroupDissolve",
      params: { id: id },
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "解散成功",
          });
          getFlatList();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  // 编辑组
  const menuItems = [
    {
      key: "edit",
      icon: <EditOutlined />,
      label: "编辑",
    },
    {
      key: "disband",
      icon: <UngroupOutlined />,
      label: "解散该组",
    },
    {
      key: "delete",
      icon: <DeleteOutlined />,
      label: "删除",
    },
  ];
  return (
    <>
      {detail ? (
        <>
          {currentTab == "1" && !groupDetail && (
            <NodeGroupDetail
              groupId={groupId}
              isGroupOpen={isGroupOpen}
              groupInfo={groupInfo}
              handleBack={handleGroupBack}
              onNoteDetail={(item) => {
                setGroupDetail(true);
                handleDetail(item);
              }}
            />
          )}
          {(currentTab == "2" || (currentTab == "1" && groupDetail)) && (
            <NoteDetail detailData={detail} isOpen={isOpen} handleBack={handleBack} />
          )}
        </>
      ) : (
        <>
          <TopTitle title="便签"></TopTitle>
          <Flex className="note-container" vertical>
            <Flex className="input">
              <Input
                allowClear
                size="large"
                prefix={<SearchOutlined />}
                placeholder="搜索便签/便签组"
                style={{ width: "100%" }}
                value={searchText}
                onClick={inputFocus}
                onBlur={inputBlur}
                onChange={inputChange}
              />
            </Flex>
            <Spin spinning={loading} size="default">
              <Flex justify="space-between" ref={segmentedHeight}>
                <Segmented<string>
                  className="setup-content-tab"
                  defaultValue={currentTab == "1" ? "便签组" : "便签"}
                  options={["便签", "便签组"]}
                  onChange={handleTabChange}
                />
                {Number(notePageDataCount) > 0 && (
                  <Tag bordered={false} color="warning" icon={<ExclamationCircleOutlined />} className="tag-num">
                    未读便签 {notePageDataCount}
                  </Tag>
                )}
              </Flex>
              {currentTab == "2" && (
                <Flex gap={token.marginSM} className="note-title-tab">
                  {tagList.map((item) => {
                    return (
                      <Flex
                        key={item.key}
                        onClick={() => {
                          tabSelect(item);
                        }}
                        className={`item-tags ${selectedItem === item.key ? "selected" : ""}`}
                      >
                        {item.label}
                      </Flex>
                    );
                  })}
                </Flex>
              )}

              {currentTab == "2" && (
                <>
                  {notePageDataTotal == 0 ? (
                    <Flex vertical className="no-data">
                      <Empty />
                    </Flex>
                  ) : (
                    <>
                      <div
                        className={classNames(
                          "note-list-wrapper",
                          location.protocol === "chrome-extension:" ? "side-panel" : "content",
                        )}
                      >
                        <Flex vertical>
                          {notePageData.map((item: CopilotNote) => {
                            return (
                              <NoteCard
                                key={item.id}
                                note={item}
                                unReadNote={deleItemHandler}
                                onView={(item) => {
                                  handleDetail(item);
                                  setIsOpen(false);
                                }}
                              ></NoteCard>
                            );
                          })}
                        </Flex>
                      </div>
                    </>
                  )}
                </>
              )}

              {currentTab == "1" && (
                <Flex vertical style={{ marginTop: token.margin, height: "calc(100vh - 250px)", overflowY: "auto" }}>
                  {notePageDataTotal == 0 ? (
                    <Flex vertical className="no-data">
                      <Empty title="暂无便签组" description="请点击页面底部新增便签组" />
                    </Flex>
                  ) : (
                    <>
                      {notePageData.map((item) => {
                        return (
                          <Dropdown
                            key={item.id}
                            menu={{
                              items: menuItems,
                              style: { width: "120px" },
                              onClick: (e) => {
                                setGroupId(item.id);
                                setGroupInfo(item);
                                if (e.key == "edit") {
                                  setModalVisible(true);
                                } else if (e.key == "delete") {
                                  delGroup(item.id);
                                } else if (e.key == "disband") {
                                  disbandGroup(item.id);
                                }
                              },
                            }}
                            trigger={["contextMenu"]}
                            getPopupContainer={(trigger) => trigger.parentNode as any}
                          >
                            <Flex
                              vertical
                              className="note-group-sign"
                              onClick={() => {
                                setGroupId(item.id);
                                setGroupInfo(item);
                                setIsGroupOpen(true);
                                handleDetail(12);
                              }}
                            >
                              <Flex className="note-group" vertical>
                                <Flex justify="space-between" className="note-group-tit">
                                  <Typography.Text className="note-group-name">{item.name}</Typography.Text>
                                  <Typography.Text className="note-group-num">{item.noteCount}条</Typography.Text>
                                </Flex>
                                <Flex className="note-group-con">{item.description}</Flex>
                              </Flex>
                              <div className="note-group"></div>
                              <div className="note-group"></div>
                            </Flex>
                          </Dropdown>
                        );
                      })}
                    </>
                  )}
                  <Flex style={{ position: "absolute", bottom: "0px", left: "0px", width: "100%" }}>
                    <Button
                      style={{ width: "100%" }}
                      icon={<PlusOutlined />}
                      size="large"
                      onClick={() => {
                        setGroupId("");
                        setModalVisible(true);
                      }}
                    >
                      新增组
                    </Button>
                  </Flex>
                </Flex>
              )}
            </Spin>
          </Flex>
        </>
      )}
      <NoteGroupModal
        visible={modalVisible}
        id={groupId}
        onClose={() => {
          setModalVisible(false);
        }}
        onConfirm={() => {
          setModalVisible(false);
          setDetail("");
          getFlatList();
        }}
      />
    </>
  );
};

export default NotePage;
