/** markdown渲染组件 */
import React, { useState, ReactNode } from "react";
import ReactMarkdown from "react-markdown";
import RemarkMath from "remark-math";
import RemarkGfm from "remark-gfm";
import RemarkBreaks from "remark-breaks";
import rehypeRaw from "rehype-raw";
import ThinkBlock from "./think";
// import RemarkMermaid from "remark-mermaid"; // 引入remark-mermaid插件
import SyntaxHighLighter from "react-syntax-highlighter";
import { solarizedLight } from "react-syntax-highlighter/dist/esm/styles/hljs";
import {
  ArrowRightOutlined,
  BulbOutlined,
  CodeSandboxOutlined,
  CopyOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import { Button, Collapse, Flex, Space, theme, Tooltip } from "antd";
import useClipboard from "@/hooks/useClipboard.ts";
import { copyText } from "@/utils/clipboard.ts";
import IconFont from "@/components/IconFont";
import "katex/dist/katex.min.css";
import "./index.less";

const { useToken } = theme;

interface MarkdownProps {
  content: string;
  finished: boolean;
}

interface ImageWithOverlayProps {
  src: string;
  alt: string;
}

interface UrlComProps {
  href: string;
  children: string;
}

const Markdown: React.FC<MarkdownProps> = ({ content, finished = true }) => {
  const fetchRequest = useFetchRequest();
  const clipboard = useClipboard();
  const { token } = useToken();

  // Process think tags and extract their content
  const { processedContent, thinkBlocks } = React.useMemo(() => {
    const thinkBlocks: { [key: string]: string } = {};

    // Ensure content is a string to prevent "Cannot read properties of undefined" errors
    const safeContent = content || "";

    // 检查是否有未闭合的 think 标签
    let hasUnclosedThink = safeContent.includes("<think>") && !safeContent.includes("</think>");

    // 如果有未闭合的 think 标签，需要特殊处理
    if (hasUnclosedThink && !finished) {
      // 找到最后一个 <think> 标签的位置
      const lastThinkIndex = safeContent.lastIndexOf("<think>");
      // 提取 <think> 之前的内容和 <think> 之后的内容
      const beforeThink = safeContent.substring(0, lastThinkIndex);
      const afterThink = safeContent.substring(lastThinkIndex + 7); // 7 是 <think> 的长度

      // 处理 <think> 之前的内容中的完整 think 标签
      const processedBefore = beforeThink.replace(/<think>([\s\S]*?)<\/think>/g, (_, thinkContent) => {
        const thinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
        thinkBlocks[thinkId] = thinkContent.trim();
        return `<think data-id="${thinkId}"></think>`;
      });

      // 为未闭合的 think 标签创建一个 ID
      const unclosedThinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
      // 存储未闭合 think 标签的内容
      thinkBlocks[unclosedThinkId] = afterThink.trim();

      // 组合处理后的内容
      return {
        processedContent: `${processedBefore}<think data-id="${unclosedThinkId}"></think>`,
        thinkBlocks,
      };
    } else {
      // 正常处理完整的 think 标签
      const processed = safeContent.replace(/<think>([\s\S]*?)<\/think>/g, (_, thinkContent) => {
        // Create a unique ID for each think block
        const thinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
        // Store the think content with its ID
        thinkBlocks[thinkId] = thinkContent.trim();
        // Replace with a placeholder that won't interfere with other markdown
        return `<think data-id="${thinkId}"></think>`;
      });

      return { processedContent: processed, thinkBlocks };
    }
  }, [content, finished]);

  // const markTag = (children: ReactNode, isLi: boolean = true): ReactNode => {
  //   return isLi ? (
  //     <li>{renderChildren(children)}</li>
  //   ) : (
  //     <span>
  //       {renderChildren(children)}
  //       <br />
  //     </span>
  //   );
  // };

  // const renderChildren = (children: ReactNode): ReactNode => {
  //   return React.Children.map(children, (child) => {
  //     if (React.isValidElement(child)) {
  //       // 处理 <p> 标签
  //       if (child.type === "p") {
  //         const content = Array.isArray(child.props.children) ? (
  //           child.props.children.map((text: React.ReactNode, index: number) => (
  //             <React.Fragment key={index}>{text}</React.Fragment>
  //           ))
  //         ) : (
  //           <React.Fragment>{child.props.children}</React.Fragment>
  //         );
  //         return (
  //           <React.Fragment key={child.key || Math.random()}>
  //             {content}
  //             {/*<br />*/}
  //           </React.Fragment>
  //         );
  //       }

  //       // 处理 <ul> 标签
  //       if (child.type === "ul" || child.type === "ol") {
  //         const nestedContent = React.Children.map(child.props.children, (nestedChild: any) => {
  //           if (React.isValidElement(nestedChild) && (nestedChild.props as any).node.tagName === "li") {
  //             return markTag((nestedChild.props as any).children, false);
  //           }
  //           return nestedChild; // Return other children unmodified
  //         });
  //         return (
  //           <React.Fragment key={child.key || Math.random()}>
  //             <br />
  //             <span className="list-item">{nestedContent}</span>
  //           </React.Fragment>
  //         );
  //       }

  //       // 其他元素直接返回
  //       return child;
  //     }
  //     return child; // 如果不是有效的元素，直接返回
  //   });
  // };

  // 图片鼠标移入处理
  const ImageWithOverlay: React.FC<ImageWithOverlayProps> = ({ src, alt }) => {
    let imgUrl = `${import.meta.env["VITE_FILE_PREFIX"]}${src}`;
    const [isHovered, setIsHovered] = useState(false);
    return (
      <div
        style={{ position: "relative", display: "inline-block" }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img
          src={imgUrl}
          alt={alt}
          style={{ maxWidth: "100%", height: "auto", display: "block", borderRadius: token.borderRadiusXS }}
        />
        {isHovered && (
          <div
            style={{
              width: "24px",
              height: "24px",
              position: "absolute",
              top: "9px",
              right: "12px",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              borderRadius: "4px",
              cursor: "pointer",
              justifyContent: "center",
              alignContent: "center",
            }}
          >
            <DownloadOutlined style={{ fontSize: "14px", color: "#fff" }} />
          </div>
        )}
      </div>
    );
  };

  // 鼠标移入 链接处理
  const UrlCom: React.FC<UrlComProps> = ({ href, children }) => {
    let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf", "bin"]; // 文件格式
    const suffix = children?.split(".")[children?.split(".").length - 1]; // 获取后缀
    const isFileUrl = href.includes("/file-preview") || (href.includes("/files/tools") && fileFormat.includes(suffix)); // 是否是文件链接
    let linkUrl = "";
    if (isFileUrl) {
      linkUrl = `${import.meta.env["VITE_FILE_PREFIX"]}${href}`;
    } else {
      linkUrl = href;
    }
    return (
      <>
        {isFileUrl ? (
          <Flex style={{ position: "relative", display: "inline-block", width: "100%" }}>
            <Flex
              align="center"
              justify="space-between"
              style={{
                width: "100%",
              }}
            >
              <a
                href={linkUrl}
                download={children}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  maxWidth: "90%",
                  minWidth: "240px",
                  display: "inline-block",
                  overflow: "hidden",
                  color: token.colorLink,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {children}
              </a>
            </Flex>
          </Flex>
        ) : (
          <a
            href={href}
            target="_blank"
            style={{
              maxWidth: "90%",
              minWidth: "240px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              color: token.colorLink,
            }}
            rel="noreferrer"
          >
            {children}
          </a>
        )}
      </>
    );
  };

  // 打开代码编辑器页面
  const openBuildingUrl = (codeText: string, type: string) => {
    fetchRequest({
      api: "createShortUrl",
      params: {
        codeText,
      },
      callback: (res) => {
        if (res.code === 200) {
          // 找到最后一个斜杠的位置
          const lastSlashIndex = res.data.lastIndexOf("/");
          // 从最后一个斜杠后提取字符串
          const shortUrl = res.data.substring(lastSlashIndex + 1);
          window.open(
            `${import.meta.env["VITE_TOOLBOX_URL"]}/#/${type == "mermaid" ? "flowchart" : "interpreter"}?shortUrl=${shortUrl}&type=${type}`,
            "_blank",
          );
        }
      },
    });
  };

  const markdownComponents = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      let title = [];
      if (!inline && match && ["html", "jsx", "tsx", "vue", "mermaid"].includes(match[1])) {
        title = /<title>(.*?)<\/title>/.exec(children);
      }
      const isHtmlPage =
        typeof children === "string" &&
        (children.includes("</html>") || children.includes("</style>") || children.includes("export default"));
      return !inline && match ? (
        <>
          {["html", "jsx", "tsx", "vue", "mermaid"].includes(match[1]) ? (
            <Flex
              className="code-card"
              onClick={() => {
                if (finished || isHtmlPage) {
                  openBuildingUrl(children, match[1]);
                }
              }}
            >
              {/* <Flex className="code-card-icon">
                <IconFont type="Ts" className="icon" />
              </Flex> */}
              <Flex vertical>
                {["html"].includes(match[1]) && (
                  <Flex className="code-title">{title?.length > 0 ? title[1] : "Html"}</Flex>
                )}
                {["vue"].includes(match[1]) && <Flex className="code-title">Vue</Flex>}
                {["tsx", "jsx", "react"].includes(match[1]) && <Flex className="code-title">React</Flex>}
                {["mermaid"].includes(match[1]) && <Flex className="code-title">关系图</Flex>}
                {isHtmlPage || finished ? (
                  <Flex className="code-progress">已生成</Flex>
                ) : (
                  <Flex className="code-progress">生成中...</Flex>
                )}
              </Flex>
            </Flex>
          ) : (
            <SyntaxHighLighter
              {...props}
              children={String(children).replace(/\n$/, "")}
              style={solarizedLight}
              language={match[1]}
              showLineNumbers
            />
          )}
          {/* <Flex className="code-block-toolbar" justify="flex-end">
            <Space size={token.paddingXXS}>
              <Tooltip
                placement="top"
                title={clipboard.copied === children ? "已复制" : "复制"}
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
              >
                <Button
                  style={{
                    padding: token.paddingXXS,
                    height: "24px",
                    width: "24px",
                    border: "none",
                    backgroundColor: token.colorBgMask,
                  }}
                  icon={<CopyOutlined style={{ color: token.colorTextLightSolid, width: "12px" }} />}
                  onClick={() => {
                    copyText(children).then(() => {
                      clipboard.setCopied(children);
                      setTimeout(() => {
                        clipboard.setCopied(null);
                      }, 2000);
                    });
                  }}
                />
              </Tooltip>

              {["html", "jsx", "tsx", "vue"].includes(match[1]) && (
                <Button
                  style={{
                    padding: token.paddingXXS,
                    height: "24px",
                    width: "24px",
                    border: "none",
                    backgroundColor: token.colorBgMask,
                  }}
                  icon={<CodeSandboxOutlined style={{ color: token.colorTextLightSolid, width: "12px" }} />}
                  onClick={() => {
                    openBuildingUrl(children, match[1]);
                  }}
                />
              )}
            </Space>
          </Flex> */}
          {/* 非解释执行状态，展示代码块源数据 */}
        </>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      );
    },
    // li({ children }: any) {
    //   return markTag(children);
    // },
    a: ({ node, ...props }: any) => <UrlCom {...props} />,
    img: ({ node, ...props }: any) => <ImageWithOverlay {...props} />,
    think: (props: any) => {
      // Get the think ID from the data attribute
      const thinkId = props["data-id"];
      if (!thinkId || !thinkBlocks[thinkId]) {
        return null;
      }
      // Render the think content that was extracted during preprocessing
      // Pass the raw content string to ThinkBlock which will handle markdown rendering internally
      return <ThinkBlock finished={finished}>{thinkBlocks[thinkId]}</ThinkBlock>;
    },
  };

  return (
    <div className={`sino-markdown-body ${finished ? "" : "markdown-blink"}`}>
      <ReactMarkdown
        remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
        rehypePlugins={[rehypeRaw]}
        components={markdownComponents as any}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default Markdown;
