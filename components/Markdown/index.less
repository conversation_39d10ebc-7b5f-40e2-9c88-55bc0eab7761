.light() {
  color-scheme: light;
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-fg-default: #24292f;
  --color-fg-muted: #57606a;
  --color-fg-subtle: #6e7781;
  --color-canvas-default: transparent;
  --color-canvas-subtle: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: hsla(210, 18%, 87%, 1);
  --color-neutral-muted: rgba(175, 184, 193, 0.2);
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #cf222e;
}

.sino-markdown-body {
  padding-left: 5px;
  isolation: isolate;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  color: #101828;
  background-color: var(--color-canvas-default);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  word-wrap: break-word;
  // padding-bottom: 25px;

  ::-webkit-scrollbar {
    display: unset !important;
  }
}

.light {
  .light();
}

:root {
  .light();
}

@media (prefers-color-scheme: light) {
  :root {
    .light();
  }
}

.sino-markdown-body .octicon {
  display: inline-block;
  fill: currentColor;
  vertical-align: text-bottom;
}

.sino-markdown-body h1:hover .anchor .octicon-link:before,
.sino-markdown-body h2:hover .anchor .octicon-link:before,
.sino-markdown-body h3:hover .anchor .octicon-link:before,
.sino-markdown-body h4:hover .anchor .octicon-link:before,
.sino-markdown-body h5:hover .anchor .octicon-link:before,
.sino-markdown-body h6:hover .anchor .octicon-link:before {
  width: 16px;
  height: 16px;
  content: " ";
  display: inline-block;
  background-color: currentColor;
  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
  mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
}

.sino-markdown-body details,
.sino-markdown-body figcaption,
.sino-markdown-body figure {
  display: block;
}

.sino-markdown-body summary {
  display: list-item;
}

.sino-markdown-body [hidden] {
  display: none !important;
}

.sino-markdown-body a {
  background-color: transparent;
  color: var(--color-accent-fg);
  text-decoration: none;
}

.sino-markdown-body abbr[title] {
  border-bottom: none;
  text-decoration: underline dotted;
}

.sino-markdown-body b,
.sino-markdown-body strong {
  font-weight: var(--base-text-weight-semibold, 600);
}

.sino-markdown-body dfn {
  font-style: italic;
}

.sino-markdown-body mark {
  background-color: var(--color-attention-subtle);
  color: var(--color-fg-default);
}

.sino-markdown-body small {
  font-size: 90%;
}

.sino-markdown-body sub,
.sino-markdown-body sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.sino-markdown-body sub {
  bottom: -0.25em;
}

.sino-markdown-body sup {
  top: -0.5em;
}

.sino-markdown-body img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
  background-color: var(--color-canvas-default);
}

.sino-markdown-body code,
.sino-markdown-body kbd,
.sino-markdown-body pre,
.sino-markdown-body samp {
  font-family: monospace;
  font-size: 1em;
}

.sino-markdown-body figure {
  margin: 1em 40px;
}

.sino-markdown-body hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid var(--color-border-muted);
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: var(--color-border-default);
  border: 0;
}

.sino-markdown-body input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.sino-markdown-body [type="button"],
.sino-markdown-body [type="reset"],
.sino-markdown-body [type="submit"] {
  -webkit-appearance: button;
}

.sino-markdown-body [type="checkbox"],
.sino-markdown-body [type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

.sino-markdown-body [type="number"]::-webkit-inner-spin-button,
.sino-markdown-body [type="number"]::-webkit-outer-spin-button {
  height: auto;
}

.sino-markdown-body [type="search"]::-webkit-search-cancel-button,
.sino-markdown-body [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

.sino-markdown-body ::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

.sino-markdown-body ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

.sino-markdown-body a:hover {
  text-decoration: underline;
}

.sino-markdown-body ::placeholder {
  color: var(--color-fg-subtle);
  opacity: 1;
}

.sino-markdown-body hr::before {
  display: table;
  content: "";
}

.sino-markdown-body hr::after {
  display: table;
  clear: both;
  content: "";
}

.sino-markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  max-height: 400px;
  overflow: auto;
  width: 100%;
}

.sino-markdown-body td,
.sino-markdown-body th {
  padding: 0;
}

.sino-markdown-body details summary {
  cursor: pointer;
}

.sino-markdown-body details:not([open]) > *:not(summary) {
  display: none !important;
}

.sino-markdown-body a:focus,
.sino-markdown-body [role="button"]:focus,
.sino-markdown-body input[type="radio"]:focus,
.sino-markdown-body input[type="checkbox"]:focus {
  outline: 2px solid var(--color-accent-fg);
  outline-offset: -2px;
  box-shadow: none;
}

.sino-markdown-body a:focus:not(:focus-visible),
.sino-markdown-body [role="button"]:focus:not(:focus-visible),
.sino-markdown-body input[type="radio"]:focus:not(:focus-visible),
.sino-markdown-body input[type="checkbox"]:focus:not(:focus-visible) {
  outline: solid 1px transparent;
}

.sino-markdown-body a:focus-visible,
.sino-markdown-body [role="button"]:focus-visible,
.sino-markdown-body input[type="radio"]:focus-visible,
.sino-markdown-body input[type="checkbox"]:focus-visible {
  outline: 2px solid var(--color-accent-fg);
  outline-offset: -2px;
  box-shadow: none;
}

.sino-markdown-body a:not([class]):focus,
.sino-markdown-body a:not([class]):focus-visible,
.sino-markdown-body input[type="radio"]:focus,
.sino-markdown-body input[type="radio"]:focus-visible,
.sino-markdown-body input[type="checkbox"]:focus,
.sino-markdown-body input[type="checkbox"]:focus-visible {
  outline-offset: 0;
}

.sino-markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font:
    11px ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  line-height: 10px;
  color: var(--color-fg-default);
  vertical-align: middle;
  background-color: var(--color-canvas-subtle);
  border: solid 1px var(--color-neutral-muted);
  border-bottom-color: var(--color-neutral-muted);
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);
}

.sino-markdown-body h1,
.sino-markdown-body h2,
.sino-markdown-body h3,
.sino-markdown-body h4,
.sino-markdown-body h5,
.sino-markdown-body h6 {
  margin-top: 20px;
  margin-bottom: 12px;
  font-weight: var(--base-text-weight-semibold, 600);
  line-height: 1.25;
}

.sino-markdown-body h1 {
  font-size: 2em;
}

.sino-markdown-body h2 {
  font-size: 1.5em;
}

.sino-markdown-body h3 {
  font-size: 1.17em;
}

.sino-markdown-body h4 {
  font-size: 1em;
}

.sino-markdown-body h5 {
  font-size: 0.83em;
}

.sino-markdown-body h6 {
  font-size: 0.67em;
}

.sino-markdown-body p {
  margin-top: 0;
  margin-bottom: 8px;
  word-break: break-all;
}

.sino-markdown-body blockquote {
  margin: 0;
  padding: 0 8px;
  border-left: 2px solid #2970ff;
}
.sino-markdown-body blockquote:last-of-type{
  p{
    color: rgba(0,0,0,0.4);
    font-size:12px;
  }
}
.sino-markdown-body ul,
.sino-markdown-body ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 1em;
}

.sino-markdown-body ol {
  list-style: decimal !important;
}

.sino-markdown-body ul {
  list-style: disc;
}

.sino-markdown-body ol ol,
.sino-markdown-body ul ol {
  list-style-type: lower-roman;
}

.sino-markdown-body ul ul ol,
.sino-markdown-body ul ol ol,
.sino-markdown-body ol ul ol,
.sino-markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.sino-markdown-body dd {
  margin-left: 0;
}

.sino-markdown-body tt,
.sino-markdown-body code,
.sino-markdown-body samp {
  font-family:
    ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  font-size: 12px;
}

.sino-markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family:
    ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  font-size: 12px;
  word-wrap: normal;
}

.sino-markdown-body .octicon {
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.sino-markdown-body input::-webkit-outer-spin-button,
.sino-markdown-body input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
  appearance: none;
}

.sino-markdown-body::before {
  display: table;
  content: "";
}

.sino-markdown-body::after {
  display: table;
  clear: both;
  content: "";
}

.sino-markdown-body > *:first-child {
  margin-top: 0 !important;
}

.sino-markdown-body > *:last-child {
  margin-bottom: 0 !important;
}

.sino-markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.sino-markdown-body .absent {
  color: var(--color-danger-fg);
}

.sino-markdown-body .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.sino-markdown-body .anchor:focus {
  outline: none;
}

.sino-markdown-body p,
.sino-markdown-body blockquote,
.sino-markdown-body ul,
.sino-markdown-body ol,
.sino-markdown-body dl,
.sino-markdown-body table,
.sino-markdown-body pre,
.sino-markdown-body details {
  margin-top: 0;
  margin-bottom: 12px;
}

.sino-markdown-body blockquote > :first-child {
  margin-top: 0;
}

.sino-markdown-body blockquote > :last-child {
  margin-bottom: 0;
}

.sino-markdown-body h1 .octicon-link,
.sino-markdown-body h2 .octicon-link,
.sino-markdown-body h3 .octicon-link,
.sino-markdown-body h4 .octicon-link,
.sino-markdown-body h5 .octicon-link,
.sino-markdown-body h6 .octicon-link {
  color: var(--color-fg-default);
  vertical-align: middle;
  visibility: hidden;
}

.sino-markdown-body h1:hover .anchor,
.sino-markdown-body h2:hover .anchor,
.sino-markdown-body h3:hover .anchor,
.sino-markdown-body h4:hover .anchor,
.sino-markdown-body h5:hover .anchor,
.sino-markdown-body h6:hover .anchor {
  text-decoration: none;
}

.sino-markdown-body h1:hover .anchor .octicon-link,
.sino-markdown-body h2:hover .anchor .octicon-link,
.sino-markdown-body h3:hover .anchor .octicon-link,
.sino-markdown-body h4:hover .anchor .octicon-link,
.sino-markdown-body h5:hover .anchor .octicon-link,
.sino-markdown-body h6:hover .anchor .octicon-link {
  visibility: visible;
}

.sino-markdown-body h1 tt,
.sino-markdown-body h1 code,
.sino-markdown-body h2 tt,
.sino-markdown-body h2 code,
.sino-markdown-body h3 tt,
.sino-markdown-body h3 code,
.sino-markdown-body h4 tt,
.sino-markdown-body h4 code,
.sino-markdown-body h5 tt,
.sino-markdown-body h5 code,
.sino-markdown-body h6 tt,
.sino-markdown-body h6 code {
  padding: 0 0.2em;
  font-size: inherit;
}

.sino-markdown-body summary h1,
.sino-markdown-body summary h2,
.sino-markdown-body summary h3,
.sino-markdown-body summary h4,
.sino-markdown-body summary h5,
.sino-markdown-body summary h6 {
  display: inline-block;
}

.sino-markdown-body summary h1 .anchor,
.sino-markdown-body summary h2 .anchor,
.sino-markdown-body summary h3 .anchor,
.sino-markdown-body summary h4 .anchor,
.sino-markdown-body summary h5 .anchor,
.sino-markdown-body summary h6 .anchor {
  margin-left: -40px;
}

.sino-markdown-body summary h1,
.sino-markdown-body summary h2 {
  padding-bottom: 0;
  border-bottom: 0;
}

.sino-markdown-body ul.no-list,
.sino-markdown-body ol.no-list {
  padding: 0;
  list-style-type: none;
}

.sino-markdown-body ol[type="a"] {
  list-style-type: lower-alpha;
}

.sino-markdown-body ol[type="A"] {
  list-style-type: upper-alpha;
}

.sino-markdown-body ol[type="i"] {
  list-style-type: lower-roman;
}

.sino-markdown-body ol[type="I"] {
  list-style-type: upper-roman;
}

.sino-markdown-body ol[type="1"] {
  list-style-type: decimal;
}

.sino-markdown-body div > ol:not([type]) {
  list-style-type: decimal;
}

.sino-markdown-body ul ul,
.sino-markdown-body ul ol,
.sino-markdown-body ol ol,
.sino-markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.sino-markdown-body li > p {
  margin-top: 0.75em;
}

.sino-markdown-body li + li {
  margin-top: 0.25em;
}

.sino-markdown-body dl {
  padding: 0;
}

.sino-markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: var(--base-text-weight-semibold, 600);
}

.sino-markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.sino-markdown-body table th {
  font-weight: var(--base-text-weight-semibold, 600);
}

.sino-markdown-body table th,
.sino-markdown-body table td {
  padding: 6px;
  border: 1px solid var(--color-border-default);
  min-width: 70px;
}

.sino-markdown-body table tr {
  background-color: var(--color-canvas-default);
  border-top: 1px solid var(--color-border-muted);
}

.sino-markdown-body table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle);
}

.sino-markdown-body table img {
  background-color: transparent;
}

.sino-markdown-body img[align="right"] {
  padding-left: 20px;
}

.sino-markdown-body img[align="left"] {
  padding-right: 20px;
}

.sino-markdown-body .emoji {
  max-width: none;
  vertical-align: text-top;
  background-color: transparent;
}

.sino-markdown-body span.frame {
  display: block;
  overflow: hidden;
}

.sino-markdown-body span.frame > span {
  display: block;
  float: left;
  width: auto;
  padding: 7px;
  margin: 13px 0 0;
  overflow: hidden;
  border: 1px solid var(--color-border-default);
}

.sino-markdown-body span.frame span img {
  display: block;
  float: left;
}

.sino-markdown-body span.frame span span {
  display: block;
  padding: 5px 0 0;
  clear: both;
  color: var(--color-fg-default);
}

.sino-markdown-body span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

.sino-markdown-body span.align-center > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: center;
}

.sino-markdown-body span.align-center span img {
  margin: 0 auto;
  text-align: center;
}

.sino-markdown-body span.align-right {
  display: block;
  overflow: hidden;
  clear: both;
}

.sino-markdown-body span.align-right > span {
  display: block;
  margin: 13px 0 0;
  overflow: hidden;
  text-align: right;
}

.sino-markdown-body span.align-right span img {
  margin: 0;
  text-align: right;
}

.sino-markdown-body span.float-left {
  display: block;
  float: left;
  margin-right: 13px;
  overflow: hidden;
}

.sino-markdown-body span.float-left span {
  margin: 13px 0 0;
}

.sino-markdown-body span.float-right {
  display: block;
  float: right;
  margin-left: 13px;
  overflow: hidden;
}

.sino-markdown-body span.float-right > span {
  display: block;
  margin: 13px auto 0;
  overflow: hidden;
  text-align: right;
}

.sino-markdown-body code,
.sino-markdown-body tt {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: var(--color-neutral-muted);
  border-radius: 6px;
}

.sino-markdown-body code br,
.sino-markdown-body tt br {
  display: none;
}

.sino-markdown-body del code {
  text-decoration: inherit;
}

.sino-markdown-body samp {
  font-size: 85%;
}

.sino-markdown-body pre code {
  font-size: 100%;
}

.sino-markdown-body pre > code {
  padding: 0;
  margin: 0;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.sino-markdown-body .highlight {
  margin-bottom: 16px;
}

.sino-markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.sino-markdown-body .highlight pre,
.sino-markdown-body pre {
  padding: 8px;
  background: #fff;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  border-radius: 6px;
}

.sino-markdown-body pre code,
.sino-markdown-body pre tt {
  display: inline-block;
  max-width: 100%;
  padding: 0;
  margin: 0;
  overflow-x: auto;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.sino-markdown-body .csv-data td,
.sino-markdown-body .csv-data th {
  padding: 5px;
  overflow: hidden;
  font-size: 12px;
  line-height: 1;
  text-align: left;
  white-space: nowrap;
}

.sino-markdown-body .csv-data .blob-num {
  padding: 10px 8px 9px;
  text-align: right;
  background: var(--color-canvas-default);
  border: 0;
}

.sino-markdown-body .csv-data tr {
  border-top: 0;
}

.sino-markdown-body .csv-data th {
  font-weight: var(--base-text-weight-semibold, 600);
  background: var(--color-canvas-subtle);
  border-top: 0;
}

.sino-markdown-body [data-footnote-ref]::before {
  content: "[";
}

.sino-markdown-body [data-footnote-ref]::after {
  content: "]";
}

.sino-markdown-body .footnotes {
  font-size: 12px;
  color: var(--color-fg-muted);
  border-top: 1px solid var(--color-border-default);
}

.sino-markdown-body .footnotes ol {
  padding-left: 16px;
}

.sino-markdown-body .footnotes ol ul {
  display: inline-block;
  padding-left: 16px;
  margin-top: 16px;
}

.sino-markdown-body .footnotes li {
  position: relative;
}

.sino-markdown-body .footnotes li:target::before {
  position: absolute;
  top: -8px;
  right: -8px;
  bottom: -8px;
  left: -24px;
  pointer-events: none;
  content: "";
  border: 2px solid var(--color-accent-emphasis);
  border-radius: 6px;
}

.sino-markdown-body .footnotes li:target {
  color: var(--color-fg-default);
}

.sino-markdown-body .footnotes .data-footnote-backref g-emoji {
  font-family: monospace;
}

.sino-markdown-body .pl-c {
  color: var(--color-prettylights-syntax-comment);
}

.sino-markdown-body .pl-c1,
.sino-markdown-body .pl-s .pl-v {
  color: var(--color-prettylights-syntax-constant);
}

.sino-markdown-body .pl-e,
.sino-markdown-body .pl-en {
  color: var(--color-prettylights-syntax-entity);
}

.sino-markdown-body .pl-smi,
.sino-markdown-body .pl-s .pl-s1 {
  color: var(--color-prettylights-syntax-storage-modifier-import);
}

.sino-markdown-body .pl-ent {
  color: var(--color-prettylights-syntax-entity-tag);
}

.sino-markdown-body .pl-k {
  color: var(--color-prettylights-syntax-keyword);
}

.sino-markdown-body .pl-s,
.sino-markdown-body .pl-pds,
.sino-markdown-body .pl-s .pl-pse .pl-s1,
.sino-markdown-body .pl-sr,
.sino-markdown-body .pl-sr .pl-cce,
.sino-markdown-body .pl-sr .pl-sre,
.sino-markdown-body .pl-sr .pl-sra {
  color: var(--color-prettylights-syntax-string);
}

.sino-markdown-body .pl-v,
.sino-markdown-body .pl-smw {
  color: var(--color-prettylights-syntax-variable);
}

.sino-markdown-body .pl-bu {
  color: var(--color-prettylights-syntax-brackethighlighter-unmatched);
}

.sino-markdown-body .pl-ii {
  color: var(--color-prettylights-syntax-invalid-illegal-text);
  background-color: var(--color-prettylights-syntax-invalid-illegal-bg);
}

.sino-markdown-body .pl-c2 {
  color: var(--color-prettylights-syntax-carriage-return-text);
  background-color: var(--color-prettylights-syntax-carriage-return-bg);
}

.sino-markdown-body .pl-sr .pl-cce {
  font-weight: bold;
  color: var(--color-prettylights-syntax-string-regexp);
}

.sino-markdown-body .pl-ml {
  color: var(--color-prettylights-syntax-markup-list);
}

.sino-markdown-body .pl-mh,
.sino-markdown-body .pl-mh .pl-en,
.sino-markdown-body .pl-ms {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-heading);
}

.sino-markdown-body .pl-mi {
  font-style: italic;
  color: var(--color-prettylights-syntax-markup-italic);
}

.sino-markdown-body .pl-mb {
  font-weight: bold;
  color: var(--color-prettylights-syntax-markup-bold);
}

.sino-markdown-body .pl-md {
  color: var(--color-prettylights-syntax-markup-deleted-text);
  background-color: var(--color-prettylights-syntax-markup-deleted-bg);
}

.sino-markdown-body .pl-mi1 {
  color: var(--color-prettylights-syntax-markup-inserted-text);
  background-color: var(--color-prettylights-syntax-markup-inserted-bg);
}

.sino-markdown-body .pl-mc {
  color: var(--color-prettylights-syntax-markup-changed-text);
  background-color: var(--color-prettylights-syntax-markup-changed-bg);
}

.sino-markdown-body .pl-mi2 {
  color: var(--color-prettylights-syntax-markup-ignored-text);
  background-color: var(--color-prettylights-syntax-markup-ignored-bg);
}

.sino-markdown-body .pl-mdr {
  font-weight: bold;
  color: var(--color-prettylights-syntax-meta-diff-range);
}

.sino-markdown-body .pl-ba {
  color: var(--color-prettylights-syntax-brackethighlighter-angle);
}

.sino-markdown-body .pl-sg {
  color: var(--color-prettylights-syntax-sublimelinter-gutter-mark);
}

.sino-markdown-body .pl-corl {
  text-decoration: underline;
  color: var(--color-prettylights-syntax-constant-other-reference-link);
}

.sino-markdown-body g-emoji {
  display: inline-block;
  min-width: 1ch;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1em;
  font-style: normal !important;
  font-weight: var(--base-text-weight-normal, 400);
  line-height: 1;
  vertical-align: -0.075em;
}

.sino-markdown-body g-emoji img {
  width: 1em;
  height: 1em;
}

.sino-markdown-body .task-list-item {
  list-style-type: none;
}

.sino-markdown-body .task-list-item label {
  font-weight: var(--base-text-weight-normal, 400);
}

.sino-markdown-body .task-list-item.enabled label {
  cursor: pointer;
}

.sino-markdown-body .task-list-item + .task-list-item {
  margin-top: 4px;
}

.sino-markdown-body .task-list-item .handle {
  display: none;
}

.sino-markdown-body .task-list-item-checkbox {
  margin: 0 0.2em 0.25em -1.4em;
  vertical-align: middle;
}

.sino-markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
  margin: 0 -1.6em 0.25em 0.2em;
}

.sino-markdown-body .contains-task-list {
  position: relative;
}

.sino-markdown-body .contains-task-list:hover .task-list-item-convert-container,
.sino-markdown-body .contains-task-list:focus-within .task-list-item-convert-container {
  display: block;
  width: auto;
  height: 24px;
  overflow: visible;
  clip: auto;
}

.sino-markdown-body ::-webkit-calendar-picker-indicator {
  filter: invert(50%);
}

.chat-question .sino-markdown-body p {
  color: #2b2b2b;
}

.sino-markdown-body {
  .code-card{
    border: 1px solid var(--color-border-default);
    margin: 10px 0px;
    padding: 10px;
    width: 90%;
    border-radius: 8px;
    cursor: pointer;
    .code-card-icon{
      margin-right: 20px;
      font-size: 36px;
    }
    .code-title{
      font-size:14px;
    }
    .code-progress{
      margin-top: 8px;
      font-size: 12px;
      color:#00000040;
    }
  }
  p {
    margin: 10px auto;
  }

  pre {
    position: relative;
    display: flex !important;
    overflow-x: auto;

    &:hover .code-block-toolbar {
      display: flex;
    }

    .code-block-toolbar {
      /* 代码块的按钮功能区 */
      display: none;
      gap: 5px;
      position: absolute;
      right: 16px;
      top: 10px;
      z-index: 999;

      .action-item {
        /* 操作按钮 */
        align-items: center;
        background-color: #fff;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        font-size: 14px;
        height: 26px;
        justify-content: center;
        width: 26px;

        .icon {
          object-fit: cover;
        }
      }
    }

    pre {
      max-width: calc(100vw - 100px);
      width: 100%;

      code {
        width: 100%;
      }
    }
  }
}

.sino-markdown-body pre {
  margin: 0;
  padding: 0;
}

.markdown-blink > :not(ol):not(ul):not(pre):last-child:after,
.markdown-blink > ol:last-child > li:last-child:after,
.markdown-blink > pre:last-child code:after,
.markdown-blink > ul:last-child > li:last-child:after {
  animation: blink 1s steps(5, start) infinite;
  content: "\25cf";
  font-size: 16px;
  font-family: Noto Sans SC;
  margin-inline-start: 0.25em;
  vertical-align: baseline;
}

@keyframes blink {
  to {
    visibility: hidden;
  }
}

