/** 公共SSE对话hook */
import { useCallback, useEffect, useRef, useState } from "react";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import { useFetchSSE } from "@/hooks/useFetchSSE.ts";
import { IFetchSSEArguments } from "@/types/message";
import { useGetState } from "ahooks";

/** 输出状态枚举类型 */
export enum GenerationProgress {
  /** 初始化状态 */
  INITIALIZED,

  /** 等待服务端响应，此时服务端还没有返回任何响应体 */
  WAITING,

  /** 服务端响应中，此时服务端会不停的推送响应体 */
  RESPONDING,

  /** 服务端推送完了最后的完成响应包，此时客户端与服务端的交互完成 */
  RESPONDED,

  /** 服务端响应后所有内容均已在客户端渲染完毕，相当于单次的请求全部完成 */
  RENDER_FINISHED,

  /** 用户中途主动取消了请求 */
  USER_CANCELED = 5,

  /**
   * 服务端响应过程中发生了异常，可能是以下原因：
   * 1.网络中断
   * 2.服务端推送了错误事件
   */
  ERROR = 7,
}

export type ChatMessage = {
  id: string;
  role: "system" | "user" | "assistant";
  content: string;
  message_files?: [];
};

/** 最小渲染更新周期 */
const MIN_UPDATE_INTERVAL = 16; // 约60fps
/** 每次渲染更新的字符数 */
const CHARS_PER_UPDATE = 1;

const useSSEChat = () => {
  /** 当前会话中的SSE进度 */
  const [, setProgress, getProgress] = useGetState<GenerationProgress>(GenerationProgress.INITIALIZED);
  /** 当前会话的消息记录 */
  const chatListRef = useRef<Array<Array<ChatMessage>>>([]);
  const [, setChatList, getChatList] = useGetState<Array<Array<ChatMessage>>>([]);
  const [, setCurrentConversation, getCurrentConversation] = useGetState<string>("");
  const [messageId, setMessageId] = useState<string>("");
  /** 本次响应内容的引用 */
  const responseTextRef = useRef<string>("");
  /** 本次输出的完整响应内容 */
  const [displayedText, setDisplayedText] = useState<string>("");
  const textRef = useRef<string>("");
  const [isRendering, setIsRendering] = useState(false); // 前端是否自己渲染完毕
  const isRenderingRef = useRef(isRendering);
  /** 当前回复的id */
  const [currentResponseId, setCurrentResponseId, getCurrentResponseId] = useGetState<string>("");
  /** 服务端回复的消息id */
  const [taskId, setTaskId, getTaskId] = useGetState<string>("");

  /** 响应缓冲区，存放服务端已经推送到客户端且尚未渲染的内容 */
  const textBufferRef = useRef<string>("");
  /** 上次渲染更新的时间戳引用 */
  const lastUpdateTimeRef = useRef<number>(Date.now());
  /** 更新计时器的引用 */
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /** 处理普通Web请求的hook */
  const fetchRequest = useFetchRequest();
  /** 处理SSE请求的hook */
  const { stopSSE, fetchSSE } = useFetchSSE();

  /** 更新本次响应内容的方法 */
  const updateDisplayedText = useCallback(() => {
    // 限流：如果当前时间和上次渲染更新时间差值少于配置的最小更新周期，则不进行更新
    const now = Date.now();
    if (now - lastUpdateTimeRef.current < MIN_UPDATE_INTERVAL) return;
    // console.debug(
    //   `${now} - 当前状态: ${progressRef.current}，缓冲区长度：${textBufferRef.current.length}，输出内容：${responseTextRef.current}`,
    // );
    // if (updateIntervalRef.current && getProgress() === GenerationProgress.RESPONDED) {
    //   // 服务端响应完毕，清除计时器
    //   setDisplayedText(responseTextRef.current);
    //   clearInterval(updateIntervalRef.current);
    //   updateIntervalRef.current = null;
    //   responseTextRef.current = "";
    //   setProgress(GenerationProgress.RENDER_FINISHED);
    //   console.debug("本次SSE处理结束，销毁计时器");
    // } else if (textBufferRef.current.length > 0) {
    //   // 缓冲区中仍有内容存在，渲染没有结束
    //   // 此时根据配置定量从缓冲区中取出每次更新的文字，将其进行设置，并更新上次渲染时间
    //   const charsToAdd = textBufferRef.current.slice(0, CHARS_PER_UPDATE);
    //   setDisplayedText((prevText) => prevText + charsToAdd);
    //   textBufferRef.current = textBufferRef.current.slice(CHARS_PER_UPDATE);
    //   lastUpdateTimeRef.current = now;
    // } else if (displayedText !== responseTextRef.current) {
    //   // 如果缓冲区中没有文字，可能是服务端还没有响应过来，此时设置成本次输出的整体内容，并继续进行等待
    //   setDisplayedText(responseTextRef.current);
    // }
    const currentDisplayedText = textRef.current;
    if (
      (responseTextRef.current == currentDisplayedText &&
        textBufferRef.current.length === 0 && getProgress() !== GenerationProgress.RESPONDING) ||
      isRenderingRef.current
    ) {
      console.log('走到这儿了，要关闭的')
      // 前端自己渲染完毕
      setDisplayedText(
        responseTextRef.current.length > textBufferRef.current.length ? responseTextRef.current : textBufferRef.current,
      );

      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
      responseTextRef.current = "";
      setProgress(GenerationProgress.RENDER_FINISHED);
      setIsRendering(true);
      console.debug("本次SSE处理结束，销毁计时器");
    } else if (textBufferRef.current.length > 0) {
      // 缓冲区中仍有内容存在，渲染没有结束
      // 此时根据配置定量从缓冲区中取出每次更新的文字，将其进行设置，并更新上次渲染时间
      const charsToAdd = textBufferRef.current.slice(0, CHARS_PER_UPDATE);
      setDisplayedText((prevText) => prevText + charsToAdd);
      textBufferRef.current = textBufferRef.current.slice(CHARS_PER_UPDATE);
      lastUpdateTimeRef.current = now;
    } else if (responseTextRef.current != currentDisplayedText) {
      // 如果缓冲区中没有文字，可能是服务端还没有响应过来，此时设置成本次输出的整体内容，并继续进行等待
      // setDisplayedText((prevText) => prevText + responseTextRef.current);
      setDisplayedText(responseTextRef.current)
    }
  }, [displayedText, isRendering]);

  useEffect(() => {
    textRef.current = displayedText;
  }, [displayedText]);

  useEffect(() => {
    isRenderingRef.current = isRendering;
  }, [isRendering]);
  /** 组件销毁时清除定时器 */
  useEffect(() => {
    return () => {
      updateIntervalRef.current && clearInterval(updateIntervalRef.current);
    };
  }, []);

  /** 还原成最初始的状态 */
  const reset = useCallback(async () => {
    return new Promise((resolve) => {
      setProgress(GenerationProgress.INITIALIZED);
      chatListRef.current = [];
      setChatList([]);
      setDisplayedText("");
      setCurrentResponseId("");
      setCurrentConversation("");
      responseTextRef.current = "";
      textBufferRef.current = "";
      lastUpdateTimeRef.current = Date.now();
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
        updateIntervalRef.current = null;
      }
      resolve("");
    });
  }, [getChatList()]);

  /**
   * 发起一个SSE请求
   */
  const start = useCallback(
    ({
      message,
      url,
      headers,
      body,
      query,
      regenerate: boolean = false,
      onFinished,
    }: {
      message?: string;
      regenerate?: boolean;
      onFinished?: (result: string) => void;
    } & IFetchSSEArguments) => {
      setIsRendering(false)
      // 进入等待回复状态
      setProgress(GenerationProgress.WAITING);
      // 还原和本次响应内容有关的状态
      responseTextRef.current = "";
      textBufferRef.current = "";
      setDisplayedText("");

      // 组装用户消息
      const newMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        role: "user",
        content: message,
      };

      // 组装本次回复，用来占位，此时内容为空
      const newResponse: ChatMessage = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: "",
        message_files: [],
      };

      // 将本次内容设置到会话消息列表中
      let histotyList = []; // 历史聊天记录
      // 如果有值说明是第一次点击聊天历史回显
      if (query.conversation_id) {
        histotyList = getChatList();
      }
      if (histotyList && histotyList.length > 0) {
        chatListRef.current.push(...histotyList);
      }
      chatListRef.current.push([newMessage, newResponse]);
      setChatList(chatListRef.current);
      setCurrentResponseId(newResponse.id);
      let currentId = query.noConversationId ? "" : query.conversation_id || getCurrentConversation(); // conversation_id
      // 处理dify中的conversation_id
      const currentBody = JSON.parse(JSON.stringify(body))
      currentBody.difyJson.conversation_id = currentId || ""
      // 发送SSE
      fetchSSE({
        url,
        headers,
        body: currentBody,
        query: {
          ...query,
          conversation_id: currentId || "",
        },
        callback: (message) => {
          switch (message.process) {
            case "onmessage":
              if (getProgress() === GenerationProgress.WAITING) {
                setProgress(GenerationProgress.RESPONDING);
                setTaskId(message.task_id);
                setMessageId(message.message_id);
                setCurrentConversation(message.conversation_id);
              }
              if (message.event == "message_end") {
                if (message?.metadata?.retriever_resources && message?.metadata?.retriever_resources?.length > 0) {
                  const uniqueResources = [
                    ...new Map(
                      message.metadata.retriever_resources.map((item: any) => [
                        item.document_name, // 使用 document_name 作为 Map 的键
                        item,
                      ]),
                    ).values(),
                  ];
                  // 动态生成带文档图标的资源名称
                  const resourceItems = uniqueResources
                    .map((resource: any) => `📄 ${resource?.document_name}`)
                    .join(" \n"); // 使用全角空格分隔
                  // 构建引用块
                  const quoteBlock = `\n\n> ​**​引用​**​\n${resourceItems}`;
                  responseTextRef.current += quoteBlock;
                  textBufferRef.current += quoteBlock;
                }
              } else {
                responseTextRef.current += message.answer;
                textBufferRef.current += message.answer;
              }
              // 启动计时器进行按帧渲染
              !updateIntervalRef.current &&
                (updateIntervalRef.current = setInterval(() => {
                  requestAnimationFrame(updateDisplayedText);
                }, MIN_UPDATE_INTERVAL));
              break;
            case "onmessagefile":
              if (getProgress() === GenerationProgress.WAITING) {
                setProgress(GenerationProgress.RESPONDING);
                setTaskId(message.task_id);
                setMessageId(message.message_id);
                setCurrentConversation(message.conversation_id);
              }
              // responseTextRef.current += `![图片](https://iknow-pic.cdn.bcebos.com/b64543a98226cffc2585392fab014a90f703ea84)`;
              responseTextRef.current += `![图片](${message.url})`;
              textBufferRef.current += message.answer;

              // 启动计时器进行按帧渲染
              !updateIntervalRef.current &&
                (updateIntervalRef.current = setInterval(() => {
                  requestAnimationFrame(updateDisplayedText);
                }, MIN_UPDATE_INTERVAL));
              break;
            case "finished":
              Promise.resolve()
                .then(() => {
                  chatListRef.current[chatListRef.current.length - 1][1].content = responseTextRef.current;
                  setChatList(chatListRef.current);
                  setProgress(GenerationProgress.RENDER_FINISHED);
                  setTaskId("");
                })
                .then(() => onFinished?.(responseTextRef.current));
              break;
            case "cancel":
            case "error":
              responseTextRef.current = message.msg ? message.msg : "发生错误";
              Promise.resolve()
                .then(() => {
                  chatListRef.current[chatListRef.current.length - 1][1].content = responseTextRef.current;
                  setChatList(chatListRef.current);
                  setDisplayedText(responseTextRef.current);
                  setProgress(GenerationProgress.RENDER_FINISHED);
                  setTaskId("");
                  setIsRendering(true);
                })
                .then(() => onFinished?.(responseTextRef.current));
              if (updateIntervalRef.current) {
                clearInterval(updateIntervalRef.current);
                updateIntervalRef.current = null;
                console.debug("销毁定时器，本次sse过程结束");
              }
              break;
          }
        },
        instruct: "start",
      });
    },
    [updateDisplayedText, currentResponseId],
  );

  const stop = useCallback(
    (agentId: string, appKey: string, user: string, type = "chat") => {
      if (getTaskId()) {
        fetchRequest({
          api: "stopMessage",
          params: {
            taskId: getTaskId(),
            agentId,
            appKey,
            user,
            type,
          },
          callback: () => {
            setProgress(GenerationProgress.USER_CANCELED);
            if (updateIntervalRef.current) {
              clearInterval(updateIntervalRef.current);
              updateIntervalRef.current = null;
            }
            setIsRendering(true);
          },
        });
      } else {
        setProgress(GenerationProgress.USER_CANCELED);
        if (updateIntervalRef.current) {
          clearInterval(updateIntervalRef.current);
          updateIntervalRef.current = null;
        }
        stopSSE(); // 调用 `stopSSE` 来停止 SSE 连接
        setIsRendering(true);
      }
    },
    [fetchSSE, stopSSE],
  );

  return {
    progress: getProgress(),
    displayedText,
    chatList: getChatList(),
    currentResponseId,
    messageId,
    isRendering,
    currentConversation: getCurrentConversation(),
    start,
    stop,
    reset,
    setChatList,
    setCurrentConversation,
    setDisplayedText,
  };
};

export default useSSEChat;
