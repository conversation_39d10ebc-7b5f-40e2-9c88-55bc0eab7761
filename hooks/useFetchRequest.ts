import { useCallback, useEffect, useRef, useState } from "react";
import {
  FETCH_PORT_NAME,
  FETCH_REQUEST_TYPE,
  FETCH_RESPONSE_TYPE,
} from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import { IFetchArguments, IFetchResponseMessage } from "@/types/message";
import { Runtime } from "webextension-polyfill";
import Port = Runtime.Port;

export const useFetchRequest = () => {
  const portRef = useRef<Port | null>();
  const [isPortManuallyClosed, setIsPortManuallyClosed] = useState(false);
  const handlePort = () => {
    const newPort = browser.runtime.connect(browser.runtime.id, { name: FETCH_PORT_NAME });
    newPort.onDisconnect.addListener((port) => {
      // console.debug(`background中断了当前端口 ${FETCH_PORT_NAME} 主动销毁：${isPortManuallyClosed}`, port);
      if (!isPortManuallyClosed) {
        setTimeout(() => {
          // console.debug("尝试重新连接...");
          handlePort();
        }, 100);
      }
    });
    portRef.current = newPort;
  };
  useEffect(() => {
    handlePort();
    return () => {
      portRef.current?.disconnect();
      portRef.current = null;
      setIsPortManuallyClosed(true);
    };
  }, []);

  return useCallback((args: IFetchArguments) => {
    if (!portRef.current) handlePort();

    const messageHandler = (res: IFetchResponseMessage) => {
      if (res.type === FETCH_RESPONSE_TYPE && args.api === res.api) {
        // console.debug("content接受到了background的消息：", res);
        args.callback(res.response);
        portRef.current.onMessage.removeListener(messageHandler);
      }
    };

    portRef.current.onMessage.addListener(messageHandler);

    portRef.current.postMessage({
      api: args.api,
      type: FETCH_REQUEST_TYPE,
      params: args.params,
      file: args.file || false,
    });
  }, []);
};
